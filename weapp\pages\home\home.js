// home.js
const { request } = require('../../utils/auth');
const QQMapWX = require('../../static/map/qqmap-wx-jssdk.min.js');

let qqmapsdk;

Page({
  data: {
    posts: [],
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,
    userId: '',
    statusBarHeight: 20, // 默认状态栏高度
    selectedLocation: '' // 当前选择的位置
  },

  onLoad: function() {
    this.layout = this.selectComponent('#layout');
    // 实例化API核心类
    qqmapsdk = new QQMapWX({
      key: 'GU3BZ-2WLKT-AMOXL-LUR3F-RJHJ6-O4B3Y'  // 必填
    });

    // 获取系统信息设置状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
    
    // 获取当前用户ID
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        userId: userInfo.id || ''
      });
    }
    
    // 加载帖子列表
    this.loadPosts();
  },

  onPageScroll: function(e) {
    this.layout.handleScroll(e.scrollTop);
  },

  // 格式化时间为"多久以前"
  formatTimeAgo: function(dateString) {
    // 处理日期字符串，兼容 iOS
    let date;
    if (typeof dateString === 'string') {
      // 将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy/MM/dd HH:mm:ss" 格式
      if (dateString.includes('-')) {
        dateString = dateString.replace(/-/g, '/');
      }
      
      // 尝试创建日期对象
      date = new Date(dateString);
      
      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        console.warn('无效的日期格式:', dateString);
        return '未知时间';
      }
    } else {
      date = new Date(dateString);
    }
    
    const now = new Date();
    const diff = now - date;
    const minute = 60 * 1000;
    const hour = minute * 60;
    const day = hour * 24;
    const month = day * 30;
    const year = day * 365;

    if (diff < minute) {
      return '刚刚';
    } else if (diff < hour) {
      return Math.floor(diff / minute) + '分钟前';
    } else if (diff < day) {
      return Math.floor(diff / hour) + '小时前';
    } else if (diff < month) {
      return Math.floor(diff / day) + '天前';
    } else if (diff < year) {
      return Math.floor(diff / month) + '个月前';
    } else {
      return Math.floor(diff / year) + '年前';
    }
  },

  // 加载帖子列表
  loadPosts: async function(isRefresh = false) {
    if (this.data.loading || (!isRefresh && !this.data.hasMore)) {
      return;
    }

    this.setData({ loading: true });
    
    try {
      const res = await request({
        url: '/blade-chat/post/home-list',
        method: 'GET',
        data: {
          current: isRefresh ? 1 : this.data.pageNum,
          size: this.data.pageSize
        }
      });

      if (res.code === 200) {
        const newPosts = res.data.records || [];
        // 处理每个帖子的数据
        const formattedPosts = newPosts.map(post => ({
          ...post,
          timeAgo: this.formatTimeAgo(post.publishTime),
          // 处理图片列表
          imageList: post.images ? post.images.split(',').map(img => img.trim()).filter(img => img && img.startsWith('http')) : []
        }));

        this.setData({
          posts: isRefresh ? formattedPosts : [...this.data.posts, ...formattedPosts],
          pageNum: isRefresh ? 2 : this.data.pageNum + 1,
          hasMore: formattedPosts.length === this.data.pageSize,
          loading: false
        });
      } else {
        this.setData({
          loading: false
        });
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        });
      }
    } catch (err) {
      console.error('获取帖子列表失败：', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadPosts(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (!this.data.loading && this.data.hasMore) {
      this.loadPosts();
    }
  },

  // 跳转到帖子详情页
  navigateToDetail: function(e) {
    const postId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pkg_common/pages/post/detail/detail?id=${postId}`
    });
  },

  // 处理点赞事件
  onLike: async function(e) {
    const { id, liked } = e.detail;
    
    try {
      // 调用后端API更新点赞状态
      const res = await request({
        url: `/blade-chat/post/like/${id}`,
        method: 'POST'
      });
      
      if (res.code === 200) {
        // 更新本地帖子数据
        const posts = this.data.posts.map(post => {
          if (post.id === id) {
            return {
              ...post,
              liked: !liked,
              likeCount: liked ? (post.likeCount - 1) : (post.likeCount + 1)
            };
          }
          return post;
        });
        
        this.setData({ posts });

        wx.showToast({
          title: liked ? '已取消点赞' : '点赞成功',
          icon: 'success',
          duration: 1500
        });
      } else {
        wx.showToast({
          title: res.msg || '操作失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('点赞操作失败:', error);
      wx.showToast({
        title: '操作失败，请稍后重试',
        icon: 'none'
      });
    }
  },

  // 处理收藏事件
  onFavorite: async function(e) {
    const { id, favorite } = e.detail;
    
    try {
      // 调用后端API更新收藏状态
      const res = await request({
        url: `/blade-chat/post/favorite/${id}`,
        method: 'POST'
      });
      
      if (res.code === 200) {
        // 更新本地帖子数据
        const posts = this.data.posts.map(post => {
          if (post.id === id) {
            return {
              ...post,
              favorite: !favorite,
              favoriteCount: favorite ? (post.favoriteCount - 1) : (post.favoriteCount + 1)
            };
          }
          return post;
        });
        
        this.setData({ posts });

        wx.showToast({
          title: favorite ? '已取消收藏' : '收藏成功',
          icon: 'success',
          duration: 1500
        });
      } else {
        wx.showToast({
          title: res.msg || '操作失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('收藏操作失败:', error);
      wx.showToast({
        title: '操作失败，请稍后重试',
        icon: 'none'
      });
    }
  },

  // 添加切换帖子完成状态的方法
  onToggleCompleted: async function(e) {
    const { id } = e.detail;
    
    try {
      const res = await request({
        url: `/blade-chat/post/completed/${id}`,
        method: 'POST'
      });
      
      if (res.code === 200) {
        // 更新帖子的完成状态
        const updatedPosts = this.data.posts.map(item => {
          if (item.id === id) {
            return {
              ...item,
              completed: item.completed === 1 ? 0 : 1
            };
          }
          return item;
        });
        
        this.setData({
          posts: updatedPosts
        });
        
        wx.showToast({
          title: updatedPosts.find(item => item.id === id).completed === 1 ? '已标记为完成' : '已标记为未完成',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.msg || '操作失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('切换帖子完成状态失败:', error);
      wx.showToast({
        title: '操作失败，请稍后重试',
        icon: 'none'
      });
    }
  },

  // 处理导航栏位置选择变化
  onLocationChange(e) {
    const { location } = e.detail;
    console.log('位置选择变化:', location);
    
    // 更新选择的位置
    this.setData({
      selectedLocation: location
    });
    
    // 显示位置选择成功提示
    wx.showToast({
      title: `已选择${location}`,
      icon: 'success',
      duration: 1500
    });
    
    // 重新加载帖子列表（可以根据位置筛选）
    this.loadPosts(true);
  },

  // 添加搜索按钮点击处理
  onTapSearch: function() {
    wx.navigateTo({
      url: '/pages/search/search'
    });
  }
}); 