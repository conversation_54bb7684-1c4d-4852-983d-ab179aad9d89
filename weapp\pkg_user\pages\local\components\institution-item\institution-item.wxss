/* pkg_user/pages/local/components/institution-item/institution-item.wxss */

.institution-card {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.06);
  margin: 24rpx 24rpx 0 24rpx;
  overflow: hidden;
  position: relative;
}

.institution-card:active {
  background: #f9f9f9;
}

.institution-image-wrap {
  position: relative;
  width: 100%;
  height: 320rpx;
  overflow: hidden;
}

.institution-image {
  width: 100%;
  height: 320rpx;
  object-fit: cover;
  border-radius: 24rpx 24rpx 0 0;
}

.institution-more-btn {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  background: rgba(0,0,0,0.5);
  color: #fff;
  border-radius: 50rpx;
  padding: 8rpx 24rpx;
  font-size: 32rpx;
  z-index: 2;
}

.institution-content-wrap {
  position: relative;
  padding: 24rpx 24rpx 16rpx 24rpx;
}

/* 认证状态 */
.meta-status {
  position: absolute;
  top: 28rpx;
  right: 25rpx;
  font-size: 24rpx;
  border-radius: 8rpx;
  padding: 2rpx 16rpx;
}

.meta-status.verified {
  color: #52c41a;
  background: #eaffea;
}

.meta-status.unverified {
  color: #faad14;
  background: #fffbe6;
}

.institution-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 12rpx;
  padding-right: 100rpx; /* 为状态标签留出空间 */
}

.institution-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  line-height: 1.5;
}

/* 联系信息样式 */
.contact-info {
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 20rpx;
}

.contact-item {
  display: flex;
  font-size: 26rpx;
  line-height: 1.8;
  margin-bottom: 4rpx;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-item .label {
  color: #666;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.contact-item .value {
  color: #333;
  font-weight: 500;
  flex: 1;
}

.institution-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  color: #bbb;
  gap: 32rpx;
}

.meta-time {
  flex-shrink: 0;
}

.meta-data {
  display: flex;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-left: 40rpx;
}

.meta-item:first-child {
  margin-left: 0;
}

.meta-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.distance {
  color: #ff6b6b;
  font-weight: 500;
}
