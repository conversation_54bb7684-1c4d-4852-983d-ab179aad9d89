# 地区选择功能修复说明

## 修复的问题

1. **WXML语法错误**：修复了复杂条件判断导致的语法错误
2. **API调用参数错误**：修复了GET请求参数传递方式
3. **初始化错误**：添加了延迟加载和错误处理
4. **显示优化**：只显示最后一级地区名称（如"新安镇"而不是完整路径）
5. **底部导航栏隐藏**：弹窗显示时自动隐藏底部导航栏

## 当前状态

为了避免后端API未部署导致的错误，当前使用了模拟数据：

- 省份：江苏省
- 城市：连云港市  
- 区县：连云区、海州区、灌南县
- 乡镇：新安镇、堆沟港镇、长茂镇

## 测试方法

1. 在小程序中访问 `pages/region-test/region-test` 页面
2. 点击"选择地区"按钮
3. 测试多级选择功能

## 部署后端API后的修改

当后端API部署完成后，需要修改以下文件：

### 1. 修改 `components/region-picker/index.js`

将模拟数据替换为真实API调用：

```javascript
// 将这段代码：
const mockProvinces = [
  { regionCode: '320000', regionName: '江苏省', level: 1, hasChildren: true }
];
this.setData({
  'regionData.provinces': mockProvinces,
  loading: false
});

// 替换为：
const res = await request({
  url: '/miniapp/region/provinces',
  method: 'GET'
});
if (res.success) {
  this.setData({
    'regionData.provinces': res.data || [],
    loading: false
  });
}
```

### 2. 数据库初始化

执行 `backend/sql/miniapp_region_config.sql` 文件

### 3. 后台配置

1. 登录后台管理系统
2. 执行地区数据同步
3. 配置开放的地区

## 组件使用方法

```xml
<region-picker 
  show="{{showRegionPicker}}"
  max-level="{{4}}"
  bind:confirm="onRegionConfirm"
  bind:close="onRegionClose">
</region-picker>
```

```javascript
// 显示选择器
showRegionPicker() {
  this.setData({ showRegionPicker: true });
},

// 处理选择结果
onRegionConfirm(e) {
  const { selectedRegions, lastRegion, fullAddress } = e.detail;
  console.log('选择结果:', { selectedRegions, lastRegion, fullAddress });
},

// 关闭选择器
onRegionClose() {
  this.setData({ showRegionPicker: false });
}
```

## 新增功能

### 1. 简化地区显示
- 导航栏只显示最后一级地区名称（如"新安镇"）
- 完整地址信息仍然保存在数据中供其他功能使用

### 2. 底部导航栏自动隐藏
- 地区选择器弹窗显示时，自动隐藏底部导航栏
- 关闭弹窗时，自动显示底部导航栏
- 使用微信小程序原生API `wx.hideTabBar()` 和 `wx.showTabBar()`

## 注意事项

1. 当前版本使用模拟数据，避免API调用错误
2. 后端API部署后需要替换模拟数据为真实API调用
3. 确保数据库表已创建并初始化数据
4. 确保后台已配置开放的地区
5. 底部导航栏隐藏功能只在有TabBar的页面生效
