<!-- pkg_user/pages/local/components/institution-item/institution-item.wxml -->
<view class="institution-card" bindtap="onTap">
  <!-- 机构图片 -->
  <view class="institution-image-wrap" wx:if="{{imageList.length > 0}}">
    <image 
      class="institution-image" 
      src="{{imageList[0]}}" 
      mode="aspectFill" 
      catchtap="previewImage" 
      data-current="{{imageList[0]}}" 
    />
    <view class="institution-more-btn" catchtap="onTapMore">···</view>
  </view>
  
  <!-- 机构内容 -->
  <view class="institution-content-wrap">
    <!-- 认证状态 -->
    <text class="meta-status {{institution.verified ? 'verified' : 'unverified'}}">
      {{institution.verified ? '已认证' : '未认证'}}
    </text>
    
    <!-- 机构名称 -->
    <view class="institution-title">{{institution.name}}</view>
    
    <!-- 机构描述 -->
    <view class="institution-desc">{{institution.description || '暂无描述'}}</view>
    
    <!-- 联系信息 -->
    <view class="contact-info" wx:if="{{institution.address || institution.phone || institution.businessHours}}">
      <view class="contact-item" wx:if="{{institution.address}}">
        <text class="label">地址：</text>
        <text class="value">{{institution.address}}</text>
      </view>
      <view class="contact-item" wx:if="{{institution.phone}}">
        <text class="label">电话：</text>
        <text class="value">{{institution.phone}}</text>
      </view>
      <view class="contact-item" wx:if="{{institution.businessHours}}">
        <text class="label">营业时间：</text>
        <text class="value">{{institution.businessHours}}</text>
      </view>
    </view>
    
    <!-- 底部元数据 -->
    <view class="institution-meta">
      <text class="meta-time">{{institution.createTime || '刚刚'}}</text>
      <view class="meta-data">
        <view class="meta-item">
          <image class="meta-icon" src="/assets/images/common/view.png" mode="aspectFit"></image>
          <text>{{institution.viewCount || 0}}</text>
        </view>
        <view class="meta-item">
          <image class="meta-icon" src="/assets/images/common/like.png" mode="aspectFit"></image>
          <text>{{institution.favoriteCount || 0}}</text>
        </view>
        <view class="meta-item">
          <text class="distance">{{institution.distance || '1.2km'}}</text>
        </view>
      </view>
    </view>
  </view>
</view>
