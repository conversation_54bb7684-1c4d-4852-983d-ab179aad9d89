// 登录过期处理工具
let isRedirectingToLogin = false;

/**
 * 处理登录过期
 * @param {string} message 提示消息
 */
const handleLoginExpired = (message = '登录已过期，正在跳转登录页面') => {
  // 防止重复跳转
  if (isRedirectingToLogin) {
    console.log('正在跳转登录页面，跳过重复处理');
    return;
  }
  
  isRedirectingToLogin = true;
  
  // 清除所有认证相关的存储
  const authKeys = ['token', 'refreshToken', 'userInfo', 'hasUserInfo', 'openId'];
  authKeys.forEach(key => {
    wx.removeStorageSync(key);
  });

  // 显示提示
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 1500
  });

  // 延迟跳转，让用户看到提示
  setTimeout(() => {
    // 检查当前页面是否已经是登录页
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentRoute = currentPage ? currentPage.route : '';
    
    if (currentRoute !== 'pkg_common/pages/login/login') {
      // 跳转到登录页
      wx.navigateTo({
        url: '/pkg_common/pages/login/login',
        success: () => {
          console.log('成功跳转到登录页面');
        },
        fail: (err) => {
          console.error('跳转登录页面失败:', err);
          // 如果跳转失败，尝试使用reLaunch
          wx.reLaunch({
            url: '/pages/index/index',
            success: () => {
              console.log('使用reLaunch跳转到首页');
            },
            fail: (reLaunchErr) => {
              console.error('reLaunch也失败了:', reLaunchErr);
            }
          });
        }
      });
    } else {
      console.log('当前已在登录页面，无需跳转');
    }
    
    // 重置跳转状态
    setTimeout(() => {
      isRedirectingToLogin = false;
    }, 2000);
  }, 1500);
};

/**
 * 检查登录状态
 * @returns {boolean} 是否已登录
 */
const checkLoginStatus = () => {
  const token = wx.getStorageSync('token');
  const userInfo = wx.getStorageSync('userInfo');
  return !!(token && userInfo);
};

/**
 * 强制跳转到登录页面（用于需要登录但未登录的场景）
 */
const forceLogin = () => {
  if (!checkLoginStatus()) {
    wx.showToast({
      title: '请先登录',
      icon: 'none',
      duration: 1500
    });
    
    setTimeout(() => {
      wx.navigateTo({
        url: '/pkg_common/pages/login/login'
      });
    }, 1500);
  }
};

module.exports = {
  handleLoginExpired,
  checkLoginStatus,
  forceLogin
}; 