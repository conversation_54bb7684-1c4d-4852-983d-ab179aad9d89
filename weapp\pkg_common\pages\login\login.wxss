.login-container {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  background-color: #fff;
}

.logo {
  margin: 60rpx 0;
  width: 200rpx;
  height: 200rpx;
}

.logo image {
  width: 100%;
  height: 100%;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
}

.login-btn {
  width: 100%;
  margin: 40rpx 0;
}

.btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  text-align: center;
  color: #fff;
  background: #07c160;
  transition: all 0.3s;
}

.btn.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.btn:active {
  opacity: 0.8;
}

.tips {
  font-size: 24rpx;
  color: #999;
  margin-top: 40rpx;
}

.link {
  color: #07c160;
}

.protocol-check {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin-top: 40rpx;
}

.protocol-check .link {
  color: #07c160;
  margin: 0 4rpx;
}
