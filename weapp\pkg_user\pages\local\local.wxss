/* pkg_user/pages/local/local.wxss */

/* 页面整体 */
page {
  height: 100vh;
  background: #f5f5f5;
  overflow: hidden; /* 禁止页面整体滚动 */
}

/* 滚动容器 */
.scroll-container {
  position: relative;
  background: #f5f5f5;
  box-sizing: border-box;
  height: 100vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
}

.scroll-container::-webkit-scrollbar {
  display: none;
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 1;
  width: 100%;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 吸顶分类栏 */
.sticky-category-bar {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 999;
  background: #fff;
  border-bottom: 1rpx solid #eee;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 刷新动画已移除 */

/* 主Tab区域样式 */
.main-tab-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.main-tabs {
  display: flex;
  align-items: center;
}

.main-tab-item {
  padding: 12rpx 0;
  margin-right: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
}

.main-tab-item:last-child {
  margin-right: 0;
}

.main-tab-item.active {
  color: #ff6b6b;
  font-weight: 600;
}

.main-tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 0;
  right: 0;
  height: 4rpx;
  background: #ff6b6b;
  border-radius: 2rpx;
}

/* 申请入驻按钮 */
.apply-settle-btn {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.apply-settle-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);
}

/* 卡片区域 */
.card-section {
  margin: 0 20rpx 20rpx;
}

.card-section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 分类标签 */
.category-tabs-scroll {
  margin-bottom: 20rpx;
}

.category-tabs {
  display: flex;
  padding: 0 10rpx;
}

.category-tabs.single-tab {
  justify-content: center;
}

.tab-item {
  flex-shrink: 0;
  padding: 12rpx 24rpx;
  margin: 0 10rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
  text-align: center;
  white-space: nowrap;
}

.tab-item.active {
  background: #ff6b6b;
  color: #fff;
  transform: scale(1.05);
}

.tab-item:first-child {
  margin-left: 0;
}

.tab-item:last-child {
  margin-right: 0;
}

/* 机构列表容器 */
.institutions-container {
  padding: 0;
}

/* 底部状态区域 */
.bottom-status {
  padding: 40rpx 0;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666;
}

.loading-more.preloading {
  color: #ff6b6b;
}

.reached-bottom,
.no-more {
  font-size: 26rpx;
  color: #999;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  margin: 0 20rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
}

/* 旧样式已移除，使用组件化设计 */
