Component({
  properties: {
    post: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (newVal) {
          // 处理图片字符串
          const imageList = newVal.images ? newVal.images.split(',').filter(img => img.trim()).filter(img => img && img.startsWith('http')) : [];
          this.setData({
            imageList
          });
        }
      }
    },
    // 操作区域的按钮显示控制
    showActions: {
      type: Boolean,
      value: false
    },
    showLikeAction: {
      type: Boolean,
      value: false
    },
    showFavoriteAction: {
      type: Boolean,
      value: false
    },
    showDeleteAction: {
      type: <PERSON><PERSON>an,
      value: false
    },
    showCompletedAction: {
      type: <PERSON>olean,
      value: false
    },
    userId: {
      type: String,
      value: ''
    }
  },

  data: {
    imageList: []
  },

  methods: {
    // 点击帖子跳转到详情
    onTapPost() {
      const { id } = this.data.post;
      wx.navigateTo({
        url: `/pkg_common/pages/post/detail/detail?id=${id}`
      });
    },

    // 预览图片
    previewImage(e) {
      const { current } = e.currentTarget.dataset;
      const { imageList } = this.data;
      wx.previewImage({
        current,
        urls: imageList
      });
    },

    // 点赞/取消点赞
    onTapLike() {
      const { post } = this.data;
      // 阻止事件冒泡，避免触发点击帖子的事件
      this.triggerEvent('like', { 
        id: post.id,
        liked: post.liked 
      }, { bubbles: false, composed: false });
    },

    // 收藏/取消收藏
    onTapFavorite() {
      const { post } = this.data;
      // 阻止事件冒泡，避免触发点击帖子的事件
      this.triggerEvent('favorite', {
        id: post.id,
        favorite: post.favorite
      }, { bubbles: false, composed: false });
    },

    // 删除帖子
    onTapDelete() {
      const { post } = this.data;
      wx.showModal({
        title: '提示',
        content: '确定要删除吗？',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('delete', { 
              id: post.id 
            }, { bubbles: false, composed: false });
          }
        }
      });
    },
    
    // 切换帖子完成状态
    onTapCompleted() {
      const { post } = this.data;
      wx.showModal({
        title: '提示',
        content: post.completed === 1 ? '确定要将帖子标记为未完成吗？' : '确定要将帖子标记为已完成吗？',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('completed', { 
              id: post.id,
              completed: post.completed
            }, { bubbles: false, composed: false });
          }
        }
      });
    }
  }
}); 