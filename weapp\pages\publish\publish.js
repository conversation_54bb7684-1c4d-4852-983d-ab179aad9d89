// publish.js
const PublishStore = require('../../stores/publishStore');
const PublishHelper = require('../../utils/publishHelper');
const FileValidator = require('../../utils/fileValidator');
const app = getApp();
import { getDynamicFormConfig } from '../../stores/formStore.js';

Page({
  data: {
    // 表单数据
    title: '',
    description: '',
    contactName: '',
    contactType: 'phone',
    contactNumber: '',
    location: '',
    latitude: '',
    longitude: '',
    address: '',
    
    // 图片相关
    images: [],
    imageRows: [],
    maxImageCount: 6,
    
    // 标签相关
    selectedTags: [],
    customTag: '',
    maxTagCount: 5,
    maxTagLength: 6,
    tagList: [], // 动态加载的标签列表
    categoryTags: [], // 当前分类的标签
    
    // 分类相关
    category: '',
    categoryId: '', // 分类ID
    // 发布相关
    publishTypeActive: '普通',
    loading: false,
    
    // 草稿相关
    draftId: null,
    draftCount: 0,
    
    // 字数限制
    maxContentLength: 800,
    currentContentLength: 0,
    maxTitleLength: 50,
    
    // 弹窗控制
    showTagModal: false,
    
    // 分类列表（临时数据）
    categoryList: [],

    // 新增位置处理相关
    publishLocation: '',
    isAnonymous: "0", // 是否匿名发布

    // 动态表单相关
    dynamicFormConfig: {}, // 动态表单配置
    dynamicFormData: {}    // 动态表单数据
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log('发布页面加载参数:', options);
    this.initializePage(options);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // this.handleDraftEdit();
    // this.updateDraftCount();
  },

  /**
   * 初始化页面
   */
  async initializePage(options) {
    try {
      // 处理草稿编辑
      // if (options.draftId) {
      //   this.setData({ draftId: options.draftId });
      //   await this.loadDraft(options.draftId);
      // }
      
      // 处理分类参数
      if (options.category) {
        const categoryName = decodeURIComponent(options.category);
        const categoryId = decodeURIComponent(options.categoryId);
        this.setData({ category: categoryName });
        
        if (categoryId) {
          this.setData({ categoryId: categoryId });
          // 加载该分类下的标签
          await this.loadTagsByCategory(categoryId);
          // 加载动态表单配置
          await this.loadDynamicForm(categoryId);
        }
      }
      
      // 更新草稿数量
      this.updateDraftCount();
    } catch (error) {
      console.error('页面初始化失败:', error);
    }
  },

  /**
   * 处理草稿编辑
   */
  handleDraftEdit() {
    const editDraftId = wx.getStorageSync('temp_edit_draft_id');
    if (editDraftId) {
      console.log('检测到要编辑的草稿ID:', editDraftId);
      this.setData({ draftId: editDraftId });
      this.loadDraft(editDraftId);
      wx.removeStorageSync('temp_edit_draft_id');
    }
  },

  // ==================== 表单输入处理 ====================

  /**
   * 标题输入处理
   */
  onTitleInput: function(e) {
    const value = e.detail.value;
    if (value.length > this.data.maxTitleLength) {
      wx.showToast({
        title: `标题最多输入${this.data.maxTitleLength}字`,
        icon: 'none'
      });
      this.setData({
        title: value.slice(0, this.data.maxTitleLength)
      });
      return;
    }
    
    this.setData({ title: value });
  },

  /**
   * 描述输入处理
   */
  onDescriptionInput: function(e) {
    const value = e.detail.value;
    if (value.length > this.data.maxContentLength) {
      wx.showToast({
        title: `最多输入${this.data.maxContentLength}字`,
        icon: 'none'
      });
      this.setData({
        description: value.slice(0, this.data.maxContentLength),
        currentContentLength: this.data.maxContentLength
      });
      return;
    }
    
    this.setData({
      description: value,
      currentContentLength: value.length
    });
  },

  /**
   * 联系人姓名输入
   */
  onContactNameInput(e) {
    this.setData({ contactName: e.detail.value });
  },

  /**
   * 选择联系方式类型
   */
  selectContactType(e) {
    const type = e.detail.value;
    this.setData({
      contactType: type,
      contactNumber: ''
    });
  },

  /**
   * 联系号码输入
   */
  onContactNumberInput(e) {
    this.setData({ contactNumber: e.detail.value });
  },

  /**
   * 匿名发布切换
   */
  onAnonymousChange(e) {
    this.setData({
      isAnonymous: e.detail.value
    });
  },

  // ==================== 图片处理 ====================

  /**
   * 选择图片
   */
  chooseImage: function() {
    const currentCount = this.data.images.length;
    
    if (currentCount >= this.data.maxImageCount) {
      wx.showToast({
        title: `最多上传${this.data.maxImageCount}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.chooseMedia({
      count: this.data.maxImageCount - currentCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 验证选择的图片
        const validImages = [];
        const invalidImages = [];
        
        res.tempFiles.forEach(file => {
          const validation = FileValidator.validateImage(file.tempFilePath, file.size);
          
          if (validation.isValid) {
            validImages.push({
              path: file.tempFilePath,
              size: file.size,
              type: file.type,
              isTemp: true,
              name: FileValidator.generateSafeFileName(file.tempFilePath)
            });
          } else {
            invalidImages.push({
              path: file.tempFilePath,
              errors: validation.errors
            });
          }
        });
        
        // 显示验证错误
        if (invalidImages.length > 0) {
          const errorMessage = invalidImages[0].errors[0];
          wx.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000
          });
        }
        
        // 添加有效图片
        if (validImages.length > 0) {
          const updatedImages = [...this.data.images, ...validImages];
          this.setData({ images: updatedImages });
          this.updateImageLayout();
          
          // 显示成功提示
          // if (validImages.length > 0) {
          //   wx.showToast({
          //     title: `已选择${validImages.length}张图片`,
          //     icon: 'success'
          //   });
          // }
        }
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 更新图片布局
   */
  updateImageLayout: function() {
    const images = this.data.images;
    const rows = [];
    for (let i = 0; i < images.length; i += 3) {
      rows.push(images.slice(i, i + 3));
    }
    this.setData({ imageRows: rows });
  },

  /**
   * 删除图片
   */
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index || 0;
    const images = [...this.data.images];
    images.splice(index, 1);
    
    this.setData({ images });
    this.updateImageLayout();
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const { src } = e.currentTarget.dataset;
    const urls = this.data.images.map(img => img.path);
    wx.previewImage({
      current: src,
      urls
    });
  },

  // ==================== 位置处理 ====================

  /**
   * 处理导航栏位置选择变化
   */
  onLocationChange(e) {
    const { location } = e.detail;
    console.log('位置选择变化:', location);
    
    // 更新发布位置信息
    this.setData({
      publishLocation: location
    });
    
    // 显示位置选择成功提示
    wx.showToast({
      title: `已选择${location}`,
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 选择位置
   */
  chooseLocation() {
    wx.chooseLocation({
      success: (res) => {
        console.log('选择位置成功：', res);
        this.setData({
          location: res.address ,
          latitude: res.latitude,
          longitude: res.longitude,
          address: res.name || res.address
        });
      },
      fail: (err) => {
        console.error('选择位置失败：', err);
        if (err.errMsg.indexOf('auth deny') !== -1) {
          wx.showModal({
            title: '提示',
            content: '需要您授权使用位置信息',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting();
              }
            }
          });
        }
      }
    });
  },

  // ==================== 标签处理 ====================

  /**
   * 标签点击处理
   */
  onTagTap(e) {
    const tag = e.currentTarget.dataset.tag;
    let selectedTags = this.data.selectedTags.slice();
    const idx = selectedTags.indexOf(tag);
    
    if (idx > -1) {
      selectedTags.splice(idx, 1);
    } else {
      if (selectedTags.length >= this.data.maxTagCount) {
        wx.showToast({
          title: `最多选择${this.data.maxTagCount}个标签`,
          icon: 'none'
        });
        return;
      }
      selectedTags.push(tag);
    }
    
    this.setData({ selectedTags });
  },

  /**
   * 自定义标签输入
   */
  onCustomTagInput(e) {
    this.setData({ customTag: e.detail.value });
  },

  /**
   * 添加自定义标签
   */
  addCustomTag() {
    const customTag = this.data.customTag.trim();
    if (!customTag) {
      wx.showToast({
        title: '请输入标签内容',
        icon: 'none'
      });
      return;
    }

    if (customTag.length > this.data.maxTagLength) {
      wx.showToast({
        title: `标签最多${this.data.maxTagLength}字`,
        icon: 'none'
      });
      return;
    }

    let selectedTags = this.data.selectedTags.slice();
    if (selectedTags.indexOf(customTag) > -1) {
      wx.showToast({
        title: '标签已存在',
        icon: 'none'
      });
      return;
    }

    if (selectedTags.length >= this.data.maxTagCount) {
      wx.showToast({
        title: `最多选择${this.data.maxTagCount}个标签`,
        icon: 'none'
      });
      return;
    }

    // 创建自定义标签
    this.createCustomTag(customTag);
  },

  /**
   * 创建自定义标签
   */
  async createCustomTag(tagName) {
    if (!this.data.categoryId) {
      wx.showToast({
        title: '请先选择分类',
        icon: 'none'
      });
      return;
    }

    try {
      const tagData = {
        name: tagName,
        categoryId: this.data.categoryId,
        color: '#4ECDC4'
      };

      const result = await PublishStore.createCustomTag(tagData);
      
      if (result.success) {
        // 添加到已选标签
        let selectedTags = this.data.selectedTags.slice();
        selectedTags.push(tagName);
        
        this.setData({
          selectedTags,
          customTag: ''
        });

        // 重新加载分类标签
        await this.loadTagsByCategory(this.data.categoryId);
        
        // wx.showToast({
        //   title: '标签创建成功',
        //   icon: 'success'
        // });
      } else {
        wx.showToast({
          title: result.message || '创建标签失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('创建自定义标签失败:', error);
      wx.showToast({
        title: '创建标签失败',
        icon: 'none'
      });
    }
  },

  /**
   * 删除标签
   */
  deleteTag(e) {
    const index = e.currentTarget.dataset.index;
    let selectedTags = this.data.selectedTags.slice();
    selectedTags.splice(index, 1);
    this.setData({ selectedTags });
  },

  // ==================== 弹窗控制 ====================

  /**
   * 隐藏标签选择弹窗
   */
  hideTagModal() {
    this.setData({ showTagModal: false });
  },

  /**
   * 确认标签选择并发布
   */
  confirmTagsAndPublish() {
    this.setData({ showTagModal: false });
    setTimeout(() => {
      this.confirmPublish();
    }, 300);
  },

  // ==================== 发布处理 ====================

  /**
   * 发布按钮点击
   */
  async publishPost() {
    if (this.data.loading) return;
    
    // 表单验证
    const validation = PublishHelper.validateForm(this.data);
    if (!validation.isValid) {
      wx.showToast({
        title: validation.errors[0],
        icon: 'none'
      });
      return;
    }

    // 显示标签选择弹窗
    this.setData({ showTagModal: true });
  },

  /**
   * 确认发布
   */
  async confirmPublish() {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    try {
      wx.showLoading({
        title: '发布中...',
        mask: true
      });

      // 先上传图片
      const uploadResult = await this.uploadImages();
      if (!uploadResult.success) {
        wx.hideLoading();
        wx.showToast({
          title: uploadResult.message || '图片上传失败',
          icon: 'none'
        });
        return;
      }

      // 准备发布数据
      const postData = await this.preparePostData(uploadResult.data);
      console.log('准备发布的数据:', postData);
      
      // 调用发布接口
      const publishResult = await PublishStore.createPost(postData);
      
      wx.hideLoading();

      if (publishResult.success) {
        wx.showToast({
          title: '发布成功',
          icon: 'success',
          duration: 2000
        });

        // 删除草稿
        if (this.data.draftId) {
          await PublishStore.deleteDraft(this.data.draftId);
        }

        // 跳转到发布成功页面
        wx.redirectTo({
          url: `/pages/publish/success/success?title=${encodeURIComponent(this.data.title)}&description=${encodeURIComponent(this.data.description)}&postId=${publishResult.data?.id || ''}`
        });
        return;
      } else {
        wx.showToast({
          title: publishResult.message || '发布失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('发布失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '发布失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 上传图片
   */
  async uploadImages() {
    if (!this.data.images || this.data.images.length === 0) {
      return { success: true, data: [] };
    }

    try {
      // 使用PublishStore中的上传方法
      const result = await PublishStore.uploadImages(
        this.data.images,
        'post',
        null // 发布时还没有帖子ID，先传null
      );

      return result;
    } catch (error) {
      console.error('上传图片失败:', error);
      return {
        success: false,
        message: '图片上传失败'
      };
    }
  },

  /**
   * 准备发布数据
   */
  async preparePostData(uploadedImages = []) {
    // 构建发布数据
    const formData = {
      ...this.data,
      deviceInfo: PublishHelper.getDeviceInfo()
    };
    
    // 如果用户选择了发布位置，优先使用选择的位置
    if (this.data.publishLocation) {
      formData.publishLocation = this.data.publishLocation;
    }
    
    // 确保分类ID是数字类型
    if (this.data.categoryId) {
      formData.categoryId = parseInt(this.data.categoryId);
    }

    // 添加已上传的图片数据
    if (uploadedImages && uploadedImages.length > 0) {
      formData.images = uploadedImages;
    }

    // 添加动态表单数据
    if (Object.keys(this.data.dynamicFormData).length > 0) {
      formData.dynamicFormData = this.data.dynamicFormData;
    }
    
    return PublishHelper.buildPostData(formData);
  },

  // ==================== 草稿处理 ====================

  /**
   * 保存草稿
   */
  async saveDraft() {
    if (!this.data.description.trim()) {
      wx.showToast({
        title: '请输入内容描述',
        icon: 'none'
      });
      return;
    }

    try {
      const draftData = PublishHelper.buildDraftData(this.data);
      const result = await PublishStore.saveDraft(draftData, this.data.draftId);
      
      if (result.success) {
        wx.showToast({
          title: '草稿保存成功',
          icon: 'success'
        });
        
        if (!this.data.draftId) {
          this.setData({ draftId: result.data });
        }
        
        this.updateDraftCount();
      } else {
        wx.showToast({
          title: result.message || '保存失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('保存草稿失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载草稿
   */
  async loadDraft(draftId) {
    try {
      const result = await PublishStore.loadDraft(draftId);
      
      if (result.success) {
        const draft = result.data;
        this.setData({
          title: draft.title || '',
          description: draft.description || '',
          contactName: draft.contactName || '',
          contactType: draft.contactType || 'phone',
          contactNumber: draft.contactNumber || '',
          location: draft.location || '',
          latitude: draft.latitude || '',
          longitude: draft.longitude || '',
          selectedTags: draft.tags ? JSON.parse(draft.tags) : [],
          images: draft.images ? JSON.parse(draft.images) : [],
          category: draft.category || '',
          categoryId: draft.categoryId || '',
          isAnonymous: draft.isAnonymous || "0", // 加载草稿时也加载匿名状态
          dynamicFormData: draft.dynamicFormData ? JSON.parse(draft.dynamicFormData) : {} // 加载草稿时也加载动态表单数据
        });
        
        // 如果有分类ID，加载对应的标签
        if (draft.categoryId) {
          await this.loadTagsByCategory(draft.categoryId);
          // 加载动态表单配置
          await this.loadDynamicForm(draft.categoryId);
        }
        
        this.updateImageLayout();
      } else {
        wx.showToast({
          title: result.message || '加载草稿失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载草稿失败:', error);
      wx.showToast({
        title: '加载草稿失败',
        icon: 'none'
      });
    }
  },

  /**
   * 更新草稿数量
   */
  async updateDraftCount() {
    try {
      const result = await PublishStore.getDraftCount();
      if (result.success) {
        this.setData({ draftCount: result.count });
      }
    } catch (error) {
      console.error('获取草稿数量失败:', error);
    }
  },

  // ==================== 分类处理 ====================

  /**
   * 加载分类列表
   */
  async loadCategories() {
    try {
      const result = await PublishStore.loadCategories();
      
      if (result.success) {
        this.setData({ categories: result.data });
        console.log('分类列表:', result.data);
      } else {
        wx.showToast({
          title: result.message || '加载分类失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载分类失败:', error);
      wx.showToast({
        title: '加载分类失败',
        icon: 'none'
      });
    }
  },

  /**
   * 根据分类加载标签
   */
  async loadTagsByCategory(categoryId) {
    try {
      const result = await PublishStore.getTagsByCategory(categoryId);
      
      if (result.success) {
        this.setData({ 
          categoryTags: result.data,
          tagList: result.data.map(tag => tag.tagName) // 使用tagName字段
        });
        console.log('分类标签:', result.data);
      } else {
        console.error('加载分类标签失败:', result.message);
      }
    } catch (error) {
      console.error('加载分类标签失败:', error);
    }
  },

  /**
   * 加载动态表单配置
   */
  async loadDynamicForm(categoryId) {
    console.log('加载动态表单配置:', categoryId);
    const config = await getDynamicFormConfig(categoryId);
    console.log('loadDynamicForm', config);
    if (config) {
      this.setData({
        dynamicFormConfig: config
      });
      this.initDynamicFormData(config);
    }
  },
  initDynamicFormData(config) {
    this.setData({ dynamicFormData: config.children });
  },
  onDynamicInput(e) {
    const { field, value } = e.detail;
    console.log('onDynamicInput', field, value);
    this.setData({
      [`dynamicFormData.${field}`]: value
    });
    console.log('initDynamicFormData', this.data.dynamicFormData);
  },
  // ==================== 其他方法 ====================

  /**
   * 向标题添加标签内容
   */
  addToTitle: function(e) {
    const tag = e.currentTarget.dataset.tag;
    const newTitle = this.data.title ? `${this.data.title} ${tag}` : tag;
    this.setData({ title: newTitle });
  },

  /**
   * 向描述添加标签内容
   */
  addToDescription: function(e) {
    const tag = e.currentTarget.dataset.tag;
    const newDescription = this.data.description ? `${this.data.description} ${tag}` : tag;
    this.setData({ description: newDescription });
  }
}); 