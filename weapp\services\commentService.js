/**
 * 评论相关API服务
 */
const { request } = require('../utils/request');
const config = require('../config/api');

class CommentService {
  
  /**
   * 添加评论
   * @param {Object} commentData 评论数据
   * @param {string} commentData.postId 帖子ID
   * @param {string} commentData.content 评论内容
   * @param {string} commentData.image 图片URL（可选）
   * @param {string} commentData.contactInfo 联系信息（可选）
   * @param {Array} commentData.mentionedUserIds 提及的用户ID列表
   * @param {Array} commentData.mentionedUsers 提及的用户信息列表
   * @returns {Promise} API响应
   */
  async addComment(commentData) {
    try {
      const response = await request({
        url: config.comment.add,
        method: 'POST',
        data: commentData
      });
      return response;
    } catch (error) {
      console.error('添加评论失败:', error);
      throw error;
    }
  }

  /**
   * 回复评论
   * @param {Object} replyData 回复数据
   * @param {string} replyData.postId 帖子ID
   * @param {string} replyData.parentId 父评论ID
   * @param {string} replyData.content 回复内容
   * @param {string} replyData.image 图片URL（可选）
   * @param {string} replyData.contactInfo 联系信息（可选）
   * @param {Array} replyData.mentionedUserIds 提及的用户ID列表
   * @param {Array} replyData.mentionedUsers 提及的用户信息列表
   * @returns {Promise} API响应
   */
  async replyComment(replyData) {
    try {
      const response = await request({
        url: config.comment.reply,
        method: 'POST',
        data: replyData
      });
      return response;
    } catch (error) {
      console.error('回复评论失败:', error);
      throw error;
    }
  }

  /**
   * 获取评论列表
   * @param {string} postId 帖子ID
   * @param {Object} query 查询参数
   * @param {number} query.current 当前页码
   * @param {number} query.size 每页大小
   * @returns {Promise} API响应
   */
  async getCommentList(postId, query = {}) {
    try {
      const response = await request({
        url: config.comment.list,
        method: 'GET',
        data: {
          postId,
          current: query.current || 1,
          size: query.size || 10
        }
      });
      return response;
    } catch (error) {
      console.error('获取评论列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取评论回复列表
   * @param {string} parentId 父评论ID
   * @param {Object} query 查询参数（可选）
   * @returns {Promise} API响应
   */
  async getCommentReplies(parentId, query = {}) {
    try {
      const response = await request({
        url: `${config.comment.replies}/${parentId}`,
        method: 'GET',
        data: query
      });
      return response;
    } catch (error) {
      console.error('获取回复列表失败:', error);
      throw error;
    }
  }

  /**
   * 点赞/取消点赞评论
   * @param {string} commentId 评论ID
   * @param {string} likeType 点赞类型，默认为'LIKE'
   * @returns {Promise} API响应
   */
  async likeComment(commentId, likeType = 'LIKE') {
    try {
      const response = await request({
        url: `${config.comment.like}/${commentId}`,
        method: 'POST',
        data: { likeType }
      });
      return response;
    } catch (error) {
      console.error('点赞评论失败:', error);
      throw error;
    }
  }

  /**
   * 删除评论
   * @param {string} commentId 评论ID
   * @returns {Promise} API响应
   */
  async removeComment(commentId) {
    try {
      const response = await request({
        url: `${config.comment.remove}/${commentId}`,
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error('删除评论失败:', error);
      throw error;
    }
  }

  /**
   * 标记反馈是否有帮助
   * @param {string} feedbackId 反馈ID
   * @returns {Promise} API响应
   */
  async toggleHelpful(feedbackId) {
    try {
      const response = await request({
        url: `${config.feedback.helpful}/${feedbackId}`,
        method: 'POST'
      });
      return response;
    } catch (error) {
      console.error('标记有帮助失败:', error);
      throw error;
    }
  }

  /**
   * 获取反馈页面数据（帖子详情页面使用）
   * @param {Object} params 查询参数
   * @param {string} params.postId 帖子ID
   * @param {number} params.current 当前页码
   * @param {number} params.size 每页大小
   * @returns {Promise} API响应
   */
  async getFeedbackPage(params) {
    try {
      const response = await request({
        url: config.feedback.page,
        method: 'GET',
        data: params
      });
      return response;
    } catch (error) {
      console.error('获取反馈页面失败:', error);
      throw error;
    }
  }

  /**
   * 提交反馈
   * @param {Object} feedbackData 反馈数据
   * @returns {Promise} API响应
   */
  async submitFeedback(feedbackData) {
    try {
      const response = await request({
        url: config.feedback.submit,
        method: 'POST',
        data: feedbackData
      });
      return response;
    } catch (error) {
      console.error('提交反馈失败:', error);
      throw error;
    }
  }

  /**
   * 获取反馈标签
   * @param {string} categoryId 分类ID
   * @returns {Promise} API响应
   */
  async getFeedbackTags(categoryId) {
    try {
      const response = await request({
        url: config.feedback.tags,
        method: 'GET',
        data: { categoryId }
      });
      return response;
    } catch (error) {
      console.error('获取反馈标签失败:', error);
      throw error;
    }
  }

  /**
   * 获取热门反馈标签
   * @returns {Promise} API响应
   */
  async getHotFeedbackTags() {
    try {
      const response = await request({
        url: config.feedback.hotTags,
        method: 'GET'
      });
      return response;
    } catch (error) {
      console.error('获取热门标签失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const commentService = new CommentService();

module.exports = commentService;
