// pkg_user/pages/apply-settle/apply-settle.js
Page({
  data: {
    // 申请表单数据
    formData: {
      institutionName: '',
      contactPerson: '',
      contactPhone: '',
      businessLicense: '',
      address: '',
      businessScope: '',
      description: ''
    },
    // 提交状态
    submitting: false
  },

  onLoad(options) {
    console.log('申请入驻页面加载');
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 提交申请
  async onSubmit() {
    if (this.data.submitting) return;

    // 表单验证
    if (!this.validateForm()) {
      return;
    }

    try {
      this.setData({ submitting: true });

      // 这里调用申请入驻的API
      // const result = await request({
      //   url: '/blade-chat/institution/apply',
      //   method: 'POST',
      //   data: this.data.formData
      // });

      // 模拟提交成功
      await new Promise(resolve => setTimeout(resolve, 2000));

      wx.showModal({
        title: '申请提交成功',
        content: '您的入驻申请已提交，我们将在3个工作日内审核并联系您。',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });

    } catch (error) {
      console.error('提交申请失败:', error);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;
    
    if (!formData.institutionName.trim()) {
      wx.showToast({
        title: '请输入机构名称',
        icon: 'none'
      });
      return false;
    }

    if (!formData.contactPerson.trim()) {
      wx.showToast({
        title: '请输入联系人',
        icon: 'none'
      });
      return false;
    }

    if (!formData.contactPhone.trim()) {
      wx.showToast({
        title: '请输入联系电话',
        icon: 'none'
      });
      return false;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.contactPhone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 跳转到合作申请页面
  onGoCooperation() {
    wx.navigateTo({
      url: '/pkg_user/pages/cooperation/cooperation'
    });
  }
});
