/* home.wxss */
.container {
  padding-top: calc(var(--status-bar-height, 20px) + 54px);
  padding-left: 20rpx;
  padding-right: 20rpx;
  padding-bottom: 20rpx;
  background: #f8f8f8;
  min-height: 100vh;
  box-sizing: border-box;
}

.post-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.post-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.post-item:active {
  background: #f9f9f9;
}

.post-header {
  margin-bottom: 16rpx;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.user-meta {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.nickname {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.post-time {
  font-size: 24rpx;
  color: #999;
}

.post-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin: 16rpx 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  word-break: break-all;
}

.post-images {
  margin: 16rpx 0;
}

.image-grid {
  display: flex;
  gap: 8rpx;
}

.image-item {
  flex: 1;
  height: 220rpx;
  position: relative;
  overflow: hidden;
  border-radius: 8rpx;
}

.image-item image {
  width: 100%;
  height: 100%;
}

.more-images {
  position: relative;
}

.more-count {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
}

.post-footer {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.interaction-stats {
  display: flex;
  gap: 32rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.stat-item .count {
  color: #666;
}

/* 分类和标签样式 */
.category-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.category-tag {
  padding: 6rpx 16rpx;
  background: rgba(76, 111, 255, 0.1);
  color: #4c6fff;
  font-size: 22rpx;
  border-radius: 6rpx;
  border: 1rpx solid rgba(76, 111, 255, 0.2);
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.tag-item {
  padding: 6rpx 12rpx;
  color: #4c6fff;
  font-size: 22rpx;
  border-radius: 6rpx;
  background: rgba(76, 111, 255, 0.1);
  border: 1rpx solid rgba(76, 111, 255, 0.2);
}

/* 自定义导航栏样式 */
.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: calc(var(--status-bar-height, 20px) + 44px);
  background: #fff;
  z-index: 100;
}

/* 导航栏内容容器 */
.nav-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 32px;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  padding-left: 24rpx;
  padding-right: 120rpx;
}

.nav-location {
  display: flex;
  align-items: center;
  height: 32px;
  border-radius: 16px;
  padding: 0 16rpx;
}

.location-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 400;
  display: flex;
  align-items: center;
}

.location-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 4rpx;
  display: flex;
  align-items: center;
}

.nav-search {
  position: absolute;
  right: 200rpx;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 位置选择抽屉 */
.location-drawer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
}

.location-drawer.show {
  visibility: visible;
}

.drawer-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  opacity: 0;
  transition: opacity 0.3s;
}

.location-drawer.show .drawer-mask {
  opacity: 1;
}

.drawer-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s;
  max-height: 80vh;
  overflow-y: auto;
}

.location-drawer.show .drawer-content {
  transform: translateY(0);
}

.drawer-header {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.drawer-title {
  font-size: 32rpx;
  font-weight: 500;
}

.drawer-close {
  font-size: 40rpx;
  color: #999;
  padding: 0 20rpx;
}

.current-location {
  padding: 24rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.location-pin {
  width: 32rpx;
  height: 32rpx;
  margin-right: 2rpx;
}

.location-info {
  flex: 1;
}

.location-info .label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.location-info .value {
  font-size: 28rpx;
  color: #333;
  margin-left: 8rpx;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
}

.city-list {
  padding: 24rpx;
}

.city-section {
  margin-bottom: 32rpx;
}

.city-name {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.district-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.district-item {
  padding: 12rpx 24rpx;
  background: #f5f5f5;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #333;
}

.district-item.active {
  background: #337fff;
  color: #fff;
}
