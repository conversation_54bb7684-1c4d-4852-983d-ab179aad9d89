const auth = require('../../utils/auth.js');
const { forceLogin } = require('../../utils/loginHandler');

Component({
  properties: {
    show: {
      type: Boolean,
      value: true
    }
  },

  data: {
    currentIndex: 0, // 当前激活的tab索引
    isNavigating: false, // 是否正在导航中，防止重复点击
    targetIndex: -1, // 目标页面索引，用于显示加载状态
    showLoadingOverlay: false, // 控制加载遮罩显示
    showErrorMessage: false, // 控制错误提示显示
    errorMessage: '', // 错误提示内容
    loadingTip: '正在为您切换页面...', // 加载提示文字
    tabList: [
      {
        pagePath: '/pages/index/index',
        text: '首页',
        iconPath: '/assets/images/tab/home.png',
        selectedIconPath: '/assets/images/tab/home-active.png'
      },
      {
        pagePath: '/pages/local/local',
        text: '本地',
        iconPath: '/assets/images/tab/follow.png',
        selectedIconPath: '/assets/images/tab/follow-active.png'
      },
      {
        pagePath: '/pages/publish/category/index',
        text: '发布',
        iconPath: '/assets/images/tab/publish.png',
        selectedIconPath: '/assets/images/tab/publish-active.png',
        isPublish: true
      },
      {
        pagePath: '/pkg_user/pages/group/index',
        text: '加群',
        iconPath: '/assets/images/tab/ai.png',
        selectedIconPath: '/assets/images/tab/ai-active.png'
      },
      {
        pagePath: '/pages/mine/mine',
        text: '我的',
        iconPath: '/assets/images/tab/mine.png',
        selectedIconPath: '/assets/images/tab/mine-active.png'
      }
    ]
  },

  lifetimes: {
    attached() {
      this.updateCurrentIndex();
      // 预热常用页面，减少首次加载时间
      this.preloadPages();
    }
  },

  pageLifetimes: {
    show() {
      this.updateCurrentIndex();
    }
  },
  methods: {
    // 更新当前激活的tab索引
    updateCurrentIndex() {
      const pages = getCurrentPages();
      if (!pages.length) return;

      const currentRoute = '/' + pages[pages.length - 1].route;
      const currentIndex = this.data.tabList.findIndex(tab =>
        tab.pagePath === currentRoute
      );

      if (currentIndex !== -1 && currentIndex !== this.data.currentIndex) {
        this.setData({ currentIndex });
      }
    },

    // 检查是否为当前页面
    isCurrentPage(route) {
      const pages = getCurrentPages();
      if (!pages.length) return false;
      const current = '/' + pages[pages.length - 1].route;
      const targetRoute = route.startsWith('/') ? route : '/' + route;

      return current === targetRoute;
    },

    // 通用的tab切换方法
    switchTab(index) {
      // 防止重复点击和快速切换
      if (this.data.isNavigating) {
        return;
      }

      const tabItem = this.data.tabList[index];
      if (!tabItem) {
        return;
      }

      // 检查是否为当前页面
      if (this.isCurrentPage(tabItem.pagePath)) {
        return;
      }

      // 设置导航状态和目标索引
      this.setData({
        isNavigating: true,
        targetIndex: index,
        showLoadingOverlay: false // 初始不显示遮罩
      });

      // 添加点击反馈动画
      this.triggerEvent('tabclick', { index, tabItem });

      // 立即显示自定义加载动画
      this.setData({ showLoadingOverlay: true });

      // 快速执行导航
      setTimeout(() => {
        this.navigateToPage(tabItem, index);
      }, 100);
    },

    // 导航到指定页面
    navigateToPage(tabItem, index) {
      // 发布页面特殊处理
      if (tabItem.isPublish) {
        this.handlePublish();
        return;
      }

      // 我的页面需要登录检查
      if (index === 4) {
        if (!this.checkLogin()) {
          return;
        }
      }

      // 使用更快的页面切换方式，避免空白
      this.performFastNavigation(tabItem, index);
    },

    // 处理发布页面
    handlePublish() {
      if (!this.checkLogin()) {
        return;
      }

      wx.navigateTo({
        url: '/pages/publish/category/index',
        success: () => {
          this.setData({
            isNavigating: false,
            targetIndex: -1,
            showLoadingOverlay: false
          });
        },
        fail: (err) => {
          this.setData({
            isNavigating: false,
            targetIndex: -1,
            showLoadingOverlay: false
          });

          // 使用自定义错误提示
          this.showCustomError(`打开失败: ${err.errMsg || '网络错误'}`);
        }
      });
    },

    // 检查登录状态
    checkLogin() {
      if (!auth.checkLoginStatus()) {
        this.setData({
          isNavigating: false,
          targetIndex: -1,
          showLoadingOverlay: false
        });

        // 使用自定义登录提示
        this.showCustomError('请先登录');
        setTimeout(() => {
          forceLogin();
        }, 1500);

        return false;
      }
      return true;
    },

    // 快速导航，避免空白页面
    performFastNavigation(tabItem, index) {
      // 立即更新UI状态，减少空白时间
      this.setData({
        currentIndex: index
      });

      // 根据页面类型选择最合适的导航方式
      const navigationMethod = this.getOptimalNavigationMethod(tabItem.pagePath);

      // 使用微任务确保UI更新后再导航
      Promise.resolve().then(() => {
        navigationMethod({
          url: tabItem.pagePath,
          success: () => {
            // 快速重置状态
            this.setData({
              isNavigating: false,
              targetIndex: -1,
              showLoadingOverlay: false
            });
          },
          fail: (err) => {
            // 恢复原来的索引
            this.setData({
              currentIndex: this.data.currentIndex,
              isNavigating: false,
              targetIndex: -1,
              showLoadingOverlay: false
            });

            // 使用自定义错误提示
            this.showCustomError(`跳转失败: ${err.errMsg || '网络错误'}`);
          }
        });
      });
    },

    // 获取最优的导航方法
    getOptimalNavigationMethod(pagePath) {
      // 对于tabbar页面，使用switchTab（如果是原生tabbar）
      // 对于普通页面，使用redirectTo避免页面栈过深
      // 对于发布页面，使用navigateTo保持页面栈

      if (pagePath.includes('/publish/')) {
        return wx.navigateTo;
      } else {
        // 使用redirectTo替换当前页面，避免reLaunch的空白期
        return wx.redirectTo;
      }
    },

    // 预加载页面，减少空白时间
    preloadPages() {
      // 延迟预加载，避免影响当前页面性能
      setTimeout(() => {
        try {
          // 预加载关键页面的数据或资源
          this.preloadCriticalResources();
        } catch (error) {
          console.log('页面预加载失败:', error);
        }
      }, 2000);
    },

    // 预加载关键资源
    preloadCriticalResources() {
      // 预加载图片资源
      const criticalImages = [
        '/assets/images/tab/home-active.png',
        '/assets/images/tab/follow-active.png',
        '/assets/images/tab/mine-active.png'
      ];

      criticalImages.forEach(src => {
        wx.getImageInfo({
          src: src,
          success: () => {
            console.log('预加载图片成功:', src);
          },
          fail: () => {
            // 静默失败，不影响用户体验
          }
        });
      });
    },

    // 新的tab点击方法
    onTabClick(e) {
      const { index } = e.currentTarget.dataset;
      this.switchTab(parseInt(index));
    },

    // 保留原有方法作为兼容
    goHome() {
      this.switchTab(0);
    },
    goFollow() {
      this.switchTab(1);
    },
    goPublish() {
      this.switchTab(2);
    },
    goGroup() {
      this.switchTab(3);
    },
    goMine() {
      this.switchTab(4);
    },

    // 显示自定义错误提示
    showCustomError(message) {
      this.setData({
        showErrorMessage: true,
        errorMessage: message
      });

      // 3秒后自动隐藏
      setTimeout(() => {
        this.setData({
          showErrorMessage: false,
          errorMessage: ''
        });
      }, 3000);
    }
  }
}); 