<!--my-post-item.wxml-->
<view class="post-item" bindtap="onTapPost">
  <view class="post-header">
    <view class="user-info">
      <text class="nickname">{{post.nickname || '我'}}</text>
      <text class="post-time">{{post.publishTime}}</text>
    </view>
    
    <!-- 已完成标记图片 -->
    <image wx:if="{{post.completed === 1}}" class="completed-image" src="/assets/images/common/completed.png" mode="aspectFit"></image>
  </view>
  
  <!-- 分类和标签 -->
  <view class="category-tags" wx:if="{{post.categoryName || post.tags}}">
    <view class="category-tag" wx:if="{{post.categoryName}}">{{post.categoryName}}</view>
    <view class="tag-list" wx:if="{{post.tags && post.tags.length > 0}}">
      <view class="tag-item" 
        wx:for="{{post.tags}}" 
        wx:for-item="tag"
        wx:key="*this">
        {{tag.tagName || tag}}
      </view>
    </view>
  </view>
  
  <view class="post-content">
    <text class="post-text">{{post.content}}</text>
    <view class="post-images" wx:if="{{imageList.length > 0}}">
      <view class="image-grid">
        <block wx:for="{{imageList}}" wx:for-item="image" wx:key="*this" wx:if="{{index < 3}}">
          <view class="image-item {{imageList.length > 3 && index === 2 ? 'more-images' : ''}}">
            <image src="{{image}}" mode="aspectFill" catchtap="previewImage" data-current="{{image}}"></image>
            <view class="more-count" wx:if="{{imageList.length > 3 && index === 2}}">
              +{{imageList.length - 3}}
            </view>
          </view>
        </block>
      </view>
    </view>
  </view>

  <view class="post-footer">
    <!-- 统计区域 -->
    <view class="post-stats">
      <text class="stat-item">浏览 {{post.viewCount || 0}}</text>
      <text class="stat-item {{post.liked ? 'active' : ''}}">点赞 {{post.likeCount || 0}}</text>
      <text class="stat-item {{post.favorite ? 'active' : ''}}">收藏 {{post.favoriteCount || 0}}</text>
      <!-- <text class="stat-item {{post.completed === 1 ? 'completed' : ''}}" wx:if="{{post.createUser === userId}}">
        {{post.completed === 1 ? '已完成' : '未完成'}}
      </text> -->
    </view>
    
    <!-- 操作区域 -->
    <view class="post-actions" wx:if="{{showActions}}">
      <view class="action-btn delete-btn" catchtap="onTapDelete" wx:if="{{showDeleteAction}}">
        删除
      </view>
      <view class="action-btn {{post.favorite ? 'cancel-btn' : ''}}" catchtap="onTapFavorite" wx:if="{{showFavoriteAction}}">
        {{post.favorite ? '取消收藏' : '收藏'}}
      </view>
      <view class="action-btn {{post.liked ? 'cancel-like-btn' : ''}}" catchtap="onTapLike" wx:if="{{showLikeAction}}">
        {{post.liked ? '取消点赞' : '点赞'}}
      </view>
      <view class="action-btn {{post.completed === 1 ? 'completed-btn' : ''}}" catchtap="onTapCompleted" 
            wx:if="{{showCompletedAction && post.createUser === userId}}">
        {{post.completed === 1 ? '标为未完成' : '标为已完成'}}
      </view>
    </view>
    
    <!-- 审核状态 -->
    <!-- <view class="post-status" wx:if="{{post.auditStatus}}">
      <text class="status-text {{post.auditStatus === 'PASSED' ? 'status-passed' : 'status-pending'}}">
        {{post.auditStatus === 'PASSED' ? '已通过' : '待审核'}}
      </text>
    </view> -->
  </view>
</view> 