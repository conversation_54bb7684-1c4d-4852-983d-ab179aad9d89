/* pkg_user/pages/add-card/add-card.wxss */

.add-card-container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 图标样式 */
.tool-icon, .upload-icon, .btn-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
  margin-left: auto;
}

/* 顶部工具栏 */
.toolbar {
  background: #fff;
  padding: 20rpx;
  display: flex;
  justify-content: space-around;
  border-bottom: 1rpx solid #eee;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  transition: background 0.2s ease;
}

.tool-item:active {
  background: #f8f8f8;
}

.tool-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.tool-text {
  font-size: 24rpx;
  color: #666;
}

/* 表单滚动区域 */
.form-scroll {
  flex: 1;
  padding: 20rpx;
}

/* 头像选择 */
.avatar-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.avatar-picker {
  width: 160rpx;
  height: 160rpx;
  margin: 0 auto;
  border-radius: 50%;
  overflow: hidden;
  border: 3rpx dashed #ddd;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: border-color 0.3s ease;
}

.avatar-picker:active {
  border-color: #ff6b6b;
}

.avatar-preview {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.avatar-add-icon {
  font-size: 48rpx;
  color: #ccc;
  margin-bottom: 8rpx;
}

.avatar-add-text {
  font-size: 24rpx;
  color: #999;
}

/* 表单区域 */
.form-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input:focus {
  background: #fff;
  border: 2rpx solid #ff6b6b;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 1.5;
}

.form-textarea:focus {
  background: #fff;
  border: 2rpx solid #ff6b6b;
}

/* 分类选择器 */
.category-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.category-option {
  padding: 12rpx 24rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.category-option.active {
  background: #ff6b6b;
  color: #fff;
  transform: scale(1.05);
}

/* 底部提交区域 */
.submit-section {
  background: #fff;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: scale(0.98);
}

.submit-btn.submitting {
  background: #ccc;
  box-shadow: none;
}

.submit-btn::after {
  display: none;
}
