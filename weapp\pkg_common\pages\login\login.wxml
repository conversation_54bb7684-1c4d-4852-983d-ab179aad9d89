<view class="login-container">
  <view class="logo">
    <image src="/assets/images/kimi-logo.png" mode="aspectFit"></image>
  </view>
  
  <view class="title">欢迎使用</view>
  <view class="subtitle">请授权登录以使用完整功能</view>
  
  <view class="login-btn">
    <view 
      class="btn {{loading || !checked ? 'disabled' : ''}}" 
      loading="{{loading}}"
      bindtap="handleLogin"
    >
      微信登录
    </view>
  </view>
  
  <view class="protocol-check">
    <checkbox-group bindchange="onCheckChange">
      <checkbox value="agree" checked="{{checked}}"/>
    </checkbox-group>
    <text>我已阅读并同意</text>
    <text class="link" bindtap="onProtocolTap" data-type="user">《用户协议》</text>
    <text>和</text>
    <text class="link" bindtap="onProtocolTap" data-type="privacy">《隐私政策》</text>
  </view>
</view> 