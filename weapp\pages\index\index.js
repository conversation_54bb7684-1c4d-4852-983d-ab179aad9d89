const { getBanners, getBannersFromAPI, getQuickAccess, getQuickAccessFromAPI, getCategories, getPosts, getDefaultPosts } = require('../../stores/indexStore.js');
const { getStore } = require('../../utils/util.js');
const { request } = require('../../utils/request.js');

Page({
  data: {
    banners: [],
    quickAccess: [],
    categories: [],
    posts: [],
    currentTab: 0,
    isRefreshing: false,
    navBarHeight: 88, // 默认导航栏高度
    menuButtonHeight: 0,
    coins: [],
    showTabbar: true,
    lastScrollTop: 0,
    // 新增数据
    loading: false,
    hasMore: true,
    currentPage: 1,
    pageSize: 10,
    selectedCategoryId: null,
    showStickyCategory: false,
    scrollIntoView: '',
    // 滚动加载优化相关
    scrollHeight: 0,        // 滚动区域总高度
    scrollViewHeight: 0,    // 滚动容器高度
    preloadThreshold: 0.5,  // 预加载阈值（滚动到50%时开始加载）
    isPreloading: false,    // 是否正在预加载
    reachedBottom: false,    // 是否已到达底部
    regionPickerShow: false,
  },

  // 页面加载时触发
  onLoad() {
    this.genCoins();
    // 获取custom-nav组件实例
    this.customNav = this.selectComponent('#custom-nav');
    // 页面加载时的初始化逻辑
    this.loadPageData();
    // 获取滚动容器高度
    this.getScrollViewHeight();
  },

  // 导航栏准备完成事件
  onNavReady(e) {
    const navHeight = e.detail.height;
    const menuButtonHeight = e.detail.menuButtonHeight;
    console.log('导航栏信息:', e.detail);
    this.setData({
      navBarHeight: navHeight,
      menuButtonHeight: menuButtonHeight
    });
    console.log('导航栏高度:', navHeight);
  },

  // 地区选择器显示状态变化
  onRegionPickerShow(e) {
    const { show } = e.detail;
    if (show) {
      // 隐藏底部导航栏
      wx.hideTabBar({
        animation: true
      });
    } else {
      // 显示底部导航栏
      wx.showTabBar({
        animation: true
      });
    }
  },

  // 监听页面滚动
  onScroll(e) {
    const { scrollTop, scrollHeight } = e.detail;
    const scrollViewHeight = this.data.scrollViewHeight;

    // 更新滚动相关数据
    this.setData({
      scrollHeight: scrollHeight
    });

    // 判断分类栏是否到达顶部
    wx.createSelectorQuery().select('#category-section').boundingClientRect(rect => {
      if (rect && rect.top <= this.data.navBarHeight) {
        if (!this.data.showStickyCategory) this.setData({ showStickyCategory: true });
      } else {
        if (this.data.showStickyCategory) this.setData({ showStickyCategory: false });
      }
    }).exec();

    // 控制tabbar显隐
    if (scrollTop > this.data.lastScrollTop + 10) {
      this.setData({ showTabbar: false });
    } else if (scrollTop < this.data.lastScrollTop - 10) {
      this.setData({ showTabbar: true });
    }
    this.data.lastScrollTop = scrollTop;

    // 关键：同步传递给custom-nav组件
    if (this.customNav && this.customNav.handleScroll) {
      this.customNav.handleScroll(scrollTop);
    }

    // 智能预加载逻辑
    this.handleSmartPreload(scrollTop, scrollHeight, scrollViewHeight);
  },


  // 跳转到搜索页面
  goToSearch() {
    wx.navigateTo({
      url: '/pages/search/index'
    });
  },

  // 切换标签
  // 新增方法：切换最新/附近tab
  switchMainTab(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentTab: index,
      posts: [],
      currentPage: 1,
      hasMore: true,
      reachedBottom: false,
      isPreloading: false
    });
    this.loadPostsByCategory();
  },

  // 修改switchTab：只切换分类，不切换currentTab
  async switchTab(e) {
    const index = e.currentTarget.dataset.index;
    console.log('switchTab', index);
    
    // 获取分类对象，传递分类ID
    const category = this.data.categories[index];
    const categoryId = category ? category.id : null;
    
    this.setData({
      selectedCategoryId: categoryId,
      posts: [],
      currentPage: 1,
      hasMore: true,
      reachedBottom: false,
      isPreloading: false,
      scrollIntoView: 'category-section'
    });
    // 滚动到分类栏目吸顶位置
    wx.createSelectorQuery().select('#category-sticky').boundingClientRect(rect => {
      if (rect && rect.top !== 0) {
        wx.pageScrollTo({
          scrollTop: rect.top + wx.getSystemInfoSync().windowScrollY,
          duration: 300
        });
      }
    }).exec();
    await this.loadPostsByCategory();
  },

  // 加载页面数据
  async loadPageData() {
    try {
      // 从接口获取数据
      const [banners, quickAccess, categories] = await Promise.all([
        getBannersFromAPI(),
        getQuickAccessFromAPI(),
        getCategories()
      ]);

      this.setData({
        banners,
        quickAccess,
        categories
      });

      const userLocation = getStore('userLocation');
      await this.loadPostsByCategory({
        latitude: userLocation.latitude,
        longitude: userLocation.longitude
      });
    } catch (error) {
      console.error('加载页面数据失败:', error);
      // 如果接口失败，使用默认数据
      const banners = getBanners();
      const quickAccess = getQuickAccess();
      const categories = [];
      const posts = getDefaultPosts();

      this.setData({
        banners,
        quickAccess,
        categories,
        posts
      });
    }
  },
  goToPublish() {
    console.log('goToPublish');
    wx.navigateTo({
      url: '/pages/publish/category/index'
    });
  },

  // 修改loadPostsByCategory：根据currentTab决定是否传经纬度
  async loadPostsByCategory(query = {}) {
    if (this.data.loading || !this.data.hasMore) return;
    this.setData({ loading: true });
    try {
      let params = {
        current: this.data.currentPage,
        size: this.data.pageSize,
        categoryId: this.data.selectedCategoryId,
        ...query
      };
      // 如果currentTab为1（附近），传经纬度
      if (this.data.currentTab === 1) {
        const userLocation = getStore('userLocation');
        if (userLocation) {
          params.latitude = userLocation.latitude;
          params.longitude = userLocation.longitude;
        }
      }
      const posts = await getPosts(params);
      const hasMore = posts.length === this.data.pageSize;

      if (this.data.currentPage === 1) {
        this.setData({
          posts,
          hasMore,
          reachedBottom: false // 重置底部状态
        });
      } else {
        this.setData({
          posts: [...this.data.posts, ...posts],
          hasMore,
          reachedBottom: !hasMore // 如果没有更多数据，标记为到达底部
        });
      }
    } catch (error) {
      console.error('加载帖子数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },
  // 处理点赞事件
  onLike: async function(e) {
    const { post } = e.detail;
    const id = post.id
    try {
      // 调用后端API更新点赞状态
      const res = await request({
        url: `/blade-chat/post/like/${id}`,
        method: 'POST'
      });
      
      if (res.code === 200) {
        // 更新本地帖子数据
        const posts = this.data.posts.map(post => {
          if (post.id === id) {
            return {
              ...post,
              liked: !liked,
              likeCount: liked ? (post.likeCount - 1) : (post.likeCount + 1)
            };
          }
          return post;
        });
        
        this.setData({ posts });

        wx.showToast({
          title: liked ? '已取消点赞' : '点赞成功',
          icon: 'success',
          duration: 1500
        });
      } else {
        wx.showToast({
          title: res.msg || '操作失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('点赞操作失败:', error);
      wx.showToast({
        title: '操作失败，请稍后重试',
        icon: 'none'
      });
    }
  },

  // 下拉刷新
  async onPullDownRefresh() {
    this.genCoins();
    this.setData({
      isRefreshing: true,
      currentPage: 1,
      hasMore: true,
      posts: [],
      reachedBottom: false,
      isPreloading: false
    });
    
    // 重新加载数据
    await this.loadPageData();
    
    // 1.5秒后结束刷新状态
    setTimeout(() => {
      this.setData({
        isRefreshing: false
      });
    }, 1500);
  },

  // 上拉加载更多（保留作为兜底机制）
  async onReachBottom() {
    // 如果已经到达底部或没有更多数据，不再加载
    if (this.data.loading || !this.data.hasMore || this.data.reachedBottom) {
      console.log('到达底部，停止加载');
      return;
    }

    // 如果预加载还没完成，等待预加载
    if (this.data.isPreloading) {
      console.log('预加载进行中，跳过onReachBottom');
      return;
    }

    console.log('onReachBottom触发加载');
    this.setData({
      currentPage: this.data.currentPage + 1
    });

    await this.loadPostsByCategory();
  },

  // 点击咨询按钮
  onContactTap() {
    wx.showModal({
      title: '提示',
      content: '是否联系商家？',
      success(res) {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '10086' // 这里替换为实际的联系电话
          });
        }
      }
    });
  },

  // 处理卡片咨询事件
  onCardContact(e) {
    const cardId = e.currentTarget.dataset.id;
    // 处理咨询逻辑
    console.log('联系商家', cardId);
  },

  // 处理图片点击事件
  onImageTap(e) {
    const { post, index } = e.detail;
    if (post.images && post.images.length > 0) {
      wx.previewImage({
        current: post.images[index], // 当前显示图片的链接
        urls: post.images // 需要预览的图片链接列表
      });
    }
  },

  // 处理快速访问点击事件
  onQuickAccessTap(e) {
    const { item } = e.detail;
    console.log('快速访问点击:', item);
    
    // 这里可以根据不同的菜单项进行不同的处理
    // 例如：跳转到对应的分类页面
    if (item.url && item.url.startsWith('/category/')) {
      // 跳转到分类页面
      wx.navigateTo({
        url: item.url
      });
    }
  },

  // 处理卡片分享事件
  onCardShare(e) {
    const cardId = e.currentTarget.dataset.id;
    // 处理分享逻辑
    console.log('分享帖子', cardId);
  },

  // 生成金币参数
  genCoins() {
    const coins = [];
    for (let i = 0; i < 6; i++) {
      coins.push({
        left: 10 + Math.random() * 80, // 10vw~90vw
        size: 28 + Math.random() * 18, // 28~46rpx
        delay: (i * 0.12 + Math.random() * 0.15).toFixed(2), // 0~1s
        rotate: Math.floor(Math.random() * 60 - 30), // -30~30度
        swing: (Math.random() * 6 - 3).toFixed(2) // -3~3vw
      });
    }
    this.setData({ coins });
  },

  // 获取滚动容器高度
  getScrollViewHeight() {
    wx.createSelectorQuery().in(this).select('.main-scroll-view').boundingClientRect(rect => {
      if (rect) {
        this.setData({
          scrollViewHeight: rect.height
        });
        console.log('滚动容器高度:', rect.height);
      }
    }).exec();
  },

  // 智能预加载处理
  handleSmartPreload(scrollTop, scrollHeight, scrollViewHeight) {
    // 如果没有更多数据或正在加载，直接返回
    if (!this.data.hasMore || this.data.loading || this.data.isPreloading) {
      return;
    }

    // 如果滚动容器高度还没获取到，先获取
    if (!scrollViewHeight) {
      this.getScrollViewHeight();
      return;
    }

    // 计算滚动进度
    const maxScrollTop = scrollHeight - scrollViewHeight;
    const scrollProgress = scrollTop / maxScrollTop;

    // 检查是否到达底部
    const isNearBottom = scrollTop + scrollViewHeight >= scrollHeight - 50; // 距离底部50px

    if (isNearBottom && !this.data.reachedBottom) {
      this.setData({ reachedBottom: true });
      console.log('已到达底部，停止滚动');
      return;
    }

    // 当滚动进度超过预加载阈值时，开始预加载
    if (scrollProgress >= this.data.preloadThreshold && !this.data.isPreloading) {
      console.log(`滚动进度: ${(scrollProgress * 100).toFixed(1)}%, 开始预加载下一页`);
      this.preloadNextPage();
    }
  },

  // 预加载下一页数据
  async preloadNextPage() {
    if (this.data.loading || !this.data.hasMore || this.data.isPreloading) {
      return;
    }

    this.setData({ isPreloading: true });

    try {
      // 增加页码
      const nextPage = this.data.currentPage + 1;
      this.setData({ currentPage: nextPage });

      // 加载数据
      await this.loadPostsByCategory();

      console.log(`预加载完成，当前页码: ${nextPage}`);
    } catch (error) {
      console.error('预加载失败:', error);
      // 预加载失败时回退页码
      this.setData({ currentPage: this.data.currentPage - 1 });
    } finally {
      this.setData({ isPreloading: false });
    }
  },

  onRegionPickerShowChange(e) {
    const show = e.detail.show;
    // 隐藏/显示自定义tabbar
    if (this.getTabBar) {
      const tabbar = this.getTabBar();
      if (tabbar && tabbar.setData) {
        tabbar.setData({ show: !show });
      }
    }
  },

  // 打开地图
  onOpenMap() {
    console.log('打开地图');
    wx.navigateTo({
      url: '/pages/map/map'
    });
  }
})