Component({
  properties: {
    posts: {
      type: Array,
      value: []
    }
  },
  methods: {
    onMore() {
      this.triggerEvent('more');
    },
    onPostTap(e) {
      const id = e.currentTarget.dataset.id;
      this.triggerEvent('posttap', { id });
    },
    onEdit(e) {
      e.stopPropagation && e.stopPropagation();
      const id = e.currentTarget.dataset.id;
      this.triggerEvent('edit', { id });
    },
    onDelete(e) {
      e.stopPropagation && e.stopPropagation();
      const id = e.currentTarget.dataset.id;
      console.log(id);
      
      // 调用删除接口
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这条帖子吗？删除后无法恢复。',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('delete', { id });
          }
        }
      });
    }
  }
}); 