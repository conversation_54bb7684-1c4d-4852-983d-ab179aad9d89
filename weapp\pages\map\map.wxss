/* pages/map/map.wxss */

.map-container {
  width: 100%;
  height: 100vh;
  position: relative;
}

/* 地图样式 */
.map {
  width: 100%;
  height: 100vh;
}

/* 地图控制按钮 */
.map-controls {
  position: absolute;
  top: 100rpx;
  right: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.control-btn {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
}

.control-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 加载状态 */
.map-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 40rpx;
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.location-loading {
  background: rgba(255, 107, 107, 0.9);
  color: #fff;
}

.location-loading .loading-text {
  color: #fff;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 位置状态指示器 */
.location-indicator {
  position: absolute;
  top: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  background: rgba(76, 175, 80, 0.9);
  color: #fff;
  padding: 16rpx 24rpx;
  border-radius: 25rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
  animation: fadeInOut 3s ease-in-out;
  z-index: 99;
}

.location-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.location-text {
  font-size: 24rpx;
  font-weight: 500;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: translateX(-50%) translateY(-20rpx); }
  20% { opacity: 1; transform: translateX(-50%) translateY(0); }
  80% { opacity: 1; transform: translateX(-50%) translateY(0); }
  100% { opacity: 0; transform: translateX(-50%) translateY(-20rpx); }
}

/* 地图中心指示器 */
.map-center-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 10;
}

.center-dot {
  width: 12rpx;
  height: 12rpx;
  background: #ff6b6b;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 0 4rpx rgba(255, 107, 107, 0.3);
}

.center-circle {
  width: 60rpx;
  height: 60rpx;
  border: 2rpx solid rgba(255, 107, 107, 0.5);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: pulse-ring 2s ease-out infinite;
}

@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

/* 搜索范围指示器 */
.search-info {
  position: absolute;
  top: 160rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 98;
}

.search-radius {
  font-size: 22rpx;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.post-count {
  font-size: 20rpx;
  opacity: 0.9;
}

/* 移动提示 */
.move-tip {
  position: absolute;
  bottom: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.9);
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  backdrop-filter: blur(10rpx);
  animation: fadeInOut 4s ease-in-out;
  z-index: 97;
}

.tip-text {
  font-size: 20rpx;
  color: #666;
}

/* 帖子详情弹窗 */
.post-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999; /* 提高层级，确保在地图之上 */
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 帖子头部 */
.post-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.post-author {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.post-time {
  font-size: 24rpx;
  color: #999;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
}

.close-icon {
  font-size: 36rpx;
  color: #666;
  font-weight: 300;
}

/* 帖子内容 */
.post-content {
  padding: 30rpx;
}

.post-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: block;
}

.post-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: block;
}

.post-images {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.post-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
}

/* 帖子统计 */
.post-stats {
  display: flex;
  align-items: center;
  padding: 0 30rpx 20rpx 30rpx;
  gap: 40rpx;
}

.stat-item {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.stat-text {
  font-size: 26rpx;
  color: #666;
}

.stat-text.liked {
  color: #ff6b6b;
  font-weight: 600;
}

.like-icon.liked {
  filter: hue-rotate(0deg) saturate(2) brightness(1.2);
}

.category-tag {
  background: #e3f2fd;
  color: #1976d2;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

/* 操作按钮 */
.post-actions {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx 40rpx 30rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.action-btn.secondary {
  background: #f8f8f8;
  color: #333;
  border: 1rpx solid #e0e0e0;
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.action-text {
  font-size: 28rpx;
  font-weight: 600;
}

/* 底部信息栏 */
.map-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  padding: 20rpx 30rpx;
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.info-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
}

.info-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.info-tip {
  text-align: center;
}

.tip-text {
  font-size: 22rpx;
  color: #999;
}
