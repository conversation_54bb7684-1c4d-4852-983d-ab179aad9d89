/**
 * 数据管理模块
 */

const { PostApi, FeedbackApi, CommentApi } = require('./api');
const { timeUtils, contentUtils, dataUtils, imageUtils } = require('../utils/helpers');
const { PAGINATION, DEFAULTS } = require('../config/constants');

/**
 * 帖子数据管理器
 */
class PostDataManager {
  constructor() {
    this.postData = null;
    this.isLoading = false;
  }

  /**
   * 加载帖子详情
   * @param {string} postId 帖子ID
   * @returns {Promise} 帖子数据
   */
  async loadPostDetail(postId) {
    if (this.isLoading) return this.postData;

    this.isLoading = true;
    try {
      const data = await PostApi.getDetail(postId);
      this.postData = this.formatPostData(data);
      return this.postData;
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 格式化帖子数据
   * @param {object} rawData 原始数据
   * @returns {object} 格式化后的数据
   */
  formatPostData(rawData) {
    return {
      id: dataUtils.safeIdToString(rawData.id),
      title: rawData.title || '',
      content: rawData.content || '',
      images: rawData.images || [],
      author: {
        id: dataUtils.safeIdToString(rawData.authorId),
        nickname: rawData.authorNickname || '匿名用户',
        avatar: imageUtils.getDefaultAvatar(rawData.authorAvatar)
      },
      stats: {
        viewCount: dataUtils.safeToNumber(rawData.viewCount),
        likeCount: dataUtils.safeToNumber(rawData.likeCount),
        favoriteCount: dataUtils.safeToNumber(rawData.favoriteCount),
        commentCount: dataUtils.safeToNumber(rawData.commentCount)
      },
      userActions: {
        isLiked: Boolean(rawData.isLiked),
        isFavorited: Boolean(rawData.isFavorited)
      },
      createTime: timeUtils.formatTime(rawData.createTime),
      updateTime: timeUtils.formatTime(rawData.updateTime)
    };
  }

  /**
   * 切换点赞状态
   * @param {boolean} isLike 是否点赞
   * @returns {Promise} 操作结果
   */
  async toggleLike(isLike) {
    if (!this.postData) return false;

    try {
      await PostApi.toggleLike(this.postData.id, isLike);
      
      // 更新本地数据
      this.postData.userActions.isLiked = isLike;
      this.postData.stats.likeCount += isLike ? 1 : -1;
      
      return true;
    } catch (error) {
      console.error('切换点赞状态失败:', error);
      return false;
    }
  }

  /**
   * 切换收藏状态
   * @param {boolean} isFavorite 是否收藏
   * @returns {Promise} 操作结果
   */
  async toggleFavorite(isFavorite) {
    if (!this.postData) return false;

    try {
      await PostApi.toggleFavorite(this.postData.id, isFavorite);
      
      // 更新本地数据
      this.postData.userActions.isFavorited = isFavorite;
      this.postData.stats.favoriteCount += isFavorite ? 1 : -1;
      
      return true;
    } catch (error) {
      console.error('切换收藏状态失败:', error);
      return false;
    }
  }
}

/**
 * 反馈数据管理器
 */
class FeedbackDataManager {
  constructor() {
    this.feedbackList = [];
    this.pagination = {
      current: PAGINATION.DEFAULT_PAGE,
      size: PAGINATION.FEEDBACK_PAGE_SIZE,
      total: 0,
      hasMore: true
    };
    this.isLoading = false;
    this.expandedStates = new Map(); // 存储展开状态
  }

  /**
   * 加载反馈列表
   * @param {string} postId 帖子ID
   * @param {boolean} refresh 是否刷新
   * @returns {Promise} 反馈列表
   */
  async loadFeedbackList(postId, refresh = false) {
    if (this.isLoading) return this.feedbackList;

    // 刷新时重置分页
    if (refresh) {
      this.pagination.current = PAGINATION.DEFAULT_PAGE;
      this.pagination.hasMore = true;
    }

    if (!this.pagination.hasMore) return this.feedbackList;

    this.isLoading = true;
    try {
      const params = {
        postId,
        current: this.pagination.current,
        size: this.pagination.size
      };

      const data = await FeedbackApi.getList(params);
      const formattedList = data.records.map(item => this.formatFeedbackItem(item));

      if (refresh) {
        this.feedbackList = formattedList;
      } else {
        this.feedbackList.push(...formattedList);
      }

      // 更新分页信息
      this.pagination.current = data.current + 1;
      this.pagination.total = data.total;
      this.pagination.hasMore = this.feedbackList.length < data.total;

      // 恢复展开状态
      this.restoreExpandedStates();

      return this.feedbackList;
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 格式化反馈项数据
   * @param {object} rawItem 原始数据
   * @returns {object} 格式化后的数据
   */
  formatFeedbackItem(rawItem) {
    return {
      id: dataUtils.safeIdToString(rawItem.id),
      content: rawItem.content || '',
      formattedContent: contentUtils.formatContentWithMentions(rawItem.content),
      image: rawItem.image || '',
      author: {
        id: dataUtils.safeIdToString(rawItem.userId),
        nickname: rawItem.nickname || '匿名用户',
        avatar: imageUtils.getDefaultAvatar(rawItem.avatar)
      },
      stats: {
        likes: dataUtils.safeToNumber(rawItem.likes),
        replyCount: dataUtils.safeToNumber(rawItem.replyCount)
      },
      userActions: {
        isHelpful: Boolean(rawItem.isHelpful)
      },
      replyToUserName: rawItem.replyToUserName || '',
      time: timeUtils.formatTime(rawItem.createTime),
      createTime: rawItem.createTime,
      expanded: false,
      replies: [],
      hasMoreReplies: false,
      allRepliesLoaded: false, // 是否已加载全部回复
      totalReplyCount: dataUtils.safeToNumber(rawItem.replyCount) // 总回复数
    };
  }

  /**
   * 提交反馈
   * @param {object} feedbackData 反馈数据
   * @returns {Promise} 提交结果
   */
  async submitFeedback(feedbackData) {
    try {
      const result = await FeedbackApi.submit(feedbackData);
      
      // 添加到列表顶部
      const newFeedback = this.formatFeedbackItem(result);
      this.feedbackList.unshift(newFeedback);
      
      return true;
    } catch (error) {
      console.error('提交反馈失败:', error);
      return false;
    }
  }

  /**
   * 切换反馈点赞状态
   * @param {string} feedbackId 反馈ID
   * @returns {Promise} 操作结果
   */
  async toggleFeedbackLike(feedbackId) {
    try {
      await FeedbackApi.like(feedbackId);
      
      // 更新本地数据
      const feedback = this.feedbackList.find(item => item.id === feedbackId);
      if (feedback && !feedback.userActions.isHelpful) {
        feedback.userActions.isHelpful = true;
        feedback.stats.likes += 1;
      }
      
      return true;
    } catch (error) {
      console.error('点赞反馈失败:', error);
      return false;
    }
  }

  /**
   * 设置展开状态
   * @param {string} commentId 评论ID
   * @param {boolean} expanded 是否展开
   */
  setExpandedState(commentId, expanded) {
    this.expandedStates.set(commentId, expanded);
    
    // 更新列表中的状态
    const comment = this.feedbackList.find(item => item.id === commentId);
    if (comment) {
      comment.expanded = expanded;
    }
  }

  /**
   * 恢复展开状态
   */
  restoreExpandedStates() {
    this.feedbackList.forEach(comment => {
      const expanded = this.expandedStates.get(comment.id);
      if (expanded !== undefined) {
        comment.expanded = expanded;
      }
    });
  }

  /**
   * 更新评论回复
   * @param {string} commentId 评论ID
   * @param {Array} replies 回复列表
   */
  updateCommentReplies(commentId, replies) {
    const comment = this.feedbackList.find(item => item.id === commentId);
    if (comment) {
      const formattedReplies = replies.map(reply => ({
        ...reply,
        id: dataUtils.safeIdToString(reply.id),
        avatar: imageUtils.getDefaultAvatar(reply.avatar),
        createTime: timeUtils.formatTime(reply.createTime),
        formattedContent: contentUtils.formatContentWithMentions(reply.content)
      }));

      comment.replies = formattedReplies;
      // 判断是否已加载全部回复
      comment.allRepliesLoaded = formattedReplies.length >= comment.totalReplyCount;
      comment.hasMoreReplies = !comment.allRepliesLoaded;
    }
  }
}

/**
 * 评论数据管理器
 */
class CommentDataManager {
  /**
   * 加载回复列表
   * @param {string} commentId 评论ID
   * @returns {Promise} 回复列表
   */
  async loadReplies(commentId) {
    try {
      const data = await CommentApi.getReplies(commentId);
      return data.map(reply => this.formatReplyItem(reply));
    } catch (error) {
      console.error('加载回复失败:', error);
      return [];
    }
  }

  /**
   * 格式化回复项数据
   * @param {object} rawItem 原始数据
   * @returns {object} 格式化后的数据
   */
  formatReplyItem(rawItem) {
    return {
      id: dataUtils.safeIdToString(rawItem.id),
      content: rawItem.content || '',
      formattedContent: contentUtils.formatContentWithMentions(rawItem.content),
      author: {
        id: dataUtils.safeIdToString(rawItem.userId),
        nickname: rawItem.nickname || '匿名用户',
        avatar: imageUtils.getDefaultAvatar(rawItem.avatar)
      },
      replyToUserName: rawItem.replyToUserName || '',
      createTime: timeUtils.formatTime(rawItem.createTime),
      image: rawItem.image || ''
    };
  }

  /**
   * 提交回复
   * @param {object} replyData 回复数据
   * @returns {Promise} 提交结果
   */
  async submitReply(replyData) {
    try {
      const result = await CommentApi.reply(replyData);
      return this.formatReplyItem(result);
    } catch (error) {
      console.error('提交回复失败:', error);
      throw error;
    }
  }
}

module.exports = {
  PostDataManager,
  FeedbackDataManager,
  CommentDataManager
};
