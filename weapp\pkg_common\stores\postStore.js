const { request } = require('../utils/request.js');

// 获取帖子列表（从接口获取）
const getPostListFromAPI = async (params = {}) => {
  try {
    const response = await request({
      url: '/blade-chat-open/post/home-list',
      method: 'GET',
      data: {
        current: params.current || 1,
        size: params.size || 10,
        ...(params.categoryId && { categoryId: params.categoryId }),
        ...(params.keyword && { keyword: params.keyword }),
        ...(params.latitude && { latitude: params.latitude }),
        ...(params.longitude && { longitude: params.longitude })
      }
    });
    
    if (response.code === 200 && response.success) {
      const posts = response.data.records || [];
      
      // 处理帖子数据，转换为组件需要的格式
      return {
        records: processPosts(posts),
        total: response.data.total || 0,
        size: response.data.size || 10,
        current: response.data.current || 1
      };
    } else {
      console.error('获取帖子列表失败:', response.msg);
      return [];
    }
  } catch (error) {
    console.error('请求帖子接口失败:', error);
    return [];
  }
};

// 处理帖子数据，转换为组件需要的格式
const processPosts = (posts) => {
  if (!posts || !Array.isArray(posts)) {
    return [];
  }
  
  return posts.map(post => ({
    id: post.id,
    avatar: post.avatar || '/assets/images/avatar.png',
    niackname: post.nickname || '匿名',
    title: post.title || '',
    time: formatTime(post.createTime),
    description: post.content || '',
    address: post.address || '',
    images: post.images ? post.images.split(',').filter(img => img.trim()) : [],
    views: post.viewCount || 0,
    comments: post.commentCount || 0,
    likes: post.likeCount || 0,
    tag: post.categoryName || '本地推荐',
    auditStatus: post.auditStatus || 'APPROVED',
    contactName: post.contactName || '',
    contactPhone: post.contactPhone || '',
    isHighlight: post.isTop === 1
  }));
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '刚刚';
  
  const now = new Date();
  const postTime = new Date(timeStr);
  const diff = now - postTime;
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  
  return postTime.toLocaleDateString();
};

/**
 * 获取帖子列表（优先从接口获取，失败时使用模拟数据）
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 帖子列表数据
 */
const getPostList = async (params = {}) => {
  try {
    return await getPostListFromAPI(params);
  } catch (error) {
    console.error('获取帖子列表失败，使用模拟数据:', error);
    return [];
  }
};

/**
 * 获取帖子详情
 * @param {string|number} id 帖子ID
 * @returns {Promise<Object|null>} 帖子详情数据
 */
const getPostDetail = async (id) => {
  try {
    const response = await request({
      url: `/blade-chat-open/post/detail/${id}`,
      method: 'GET'
    });
    
    if (response.code === 200 && response.success) {
      const post = response.data;
      return processPostDetail(post);
    } else {
      console.error('获取帖子详情失败:', response.msg);
      return null;
    }
  } catch (error) {
    console.error('请求帖子详情接口失败:', error);
    return null;
  }
};

/**
 * 处理帖子详情数据
 * @param {Object} post 原始帖子数据
 * @returns {Object} 处理后的帖子数据
 */
const processPostDetail = (post) => {
  if (!post) return null;

  // 经纬度与地址兼容处理
  let latitude = post.latitude, longitude = post.longitude, address = post.address;
  if (post.geoLocation) {
    try {
      const geo = typeof post.geoLocation === 'string' ? JSON.parse(post.geoLocation) : post.geoLocation;
      latitude = geo.latitude || latitude;
      longitude = geo.longitude || longitude;
      address = geo.address || address;
    } catch (e) {}
  }

  // 标签兼容处理
  let tags = [];
  if (Array.isArray(post.tags)) {
    tags = post.tags;
  } else if (typeof post.tags === 'string') {
    tags = post.tags.split(',').map(t => t.trim()).filter(Boolean);
  } else if (Array.isArray(post.tagList)) {
    tags = post.tagList;
  }

  return {
    id: post.id,
    avatar: post.avatar || '/assets/images/avatar.png',
    nickname: post.nickname || '匿名',
    title: post.title || '',
    time: formatTime(post.createTime),
    description: post.content || '',
    address,
    latitude,
    longitude,
    images: post.images ? post.images.split(',').filter(img => img.trim()) : [],
    views: post.viewCount || 0,
    comments: post.commentCount || post.feedbackCount || 0,
    likes: post.likeCount || 0,
    tag: post.categoryName || '本地推荐',
    tags,    
    contactType: post.contactType || 'phone',
    auditStatus: post.auditStatus || 'APPROVED',
    contactName: post.contactName || '',
    contactPhone: post.contactPhone || post.contactNumber || '',
    isHighlight: post.isTop === 1,
    categoryId: post.categoryId
  };
};

/**
 * 获取我的帖子列表
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 我的帖子列表数据
 */
const getMyPostList = async (params = {}) => {
  try {
    const response = await request({
      url: '/blade-miniapp/post/my-posts',
      method: 'GET',
      data: {
        openId: params.openId,
        current: params.current || 1,
        size: params.size || 10
      }
    });
    
    if (response.code === 200 && response.success) {
      const posts = response.data.records || [];
      return {
        records: processPosts(posts),
        total: response.data.total || 0,
        size: response.data.size || 10,
        current: response.data.current || 1
      };
    } else {
      console.error('获取我的帖子列表失败:', response.msg);
      return { records: [], total: 0, size: 10, current: 1 };
    }
  } catch (error) {
    console.error('请求我的帖子接口失败:', error);
    return { records: [], total: 0, size: 10, current: 1 };
  }
};

module.exports = {
  getPostList,
  getPostListFromAPI,
  getPostDetail,
  getMyPostList,
  processPosts,
  processPostDetail
}; 