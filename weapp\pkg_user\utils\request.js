// const BASE_URL = 'https://assistant.duofan.top'; // 替换为您的后端API地址
const BASE_URL = 'http://192.168.31.103'; // 替换为您的后端API地址

const { handleLoginExpired } = require('../../utils/loginHandler');

// 请求拦截器
const requestInterceptor = (config) => {
  const token = wx.getStorageSync('token').value;
  const openId = wx.getStorageSync('openId');

  config.header = {
    ...config.header,
    'Tenant-Id' :"000000",
    'Authorization': `Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U=`,
  };

  if (token) {
    config.header = {
      ...config.header,
      'Blade-Auth': `Bearer ${token}`
    };
  }

  // 添加OpenID头
  if (openId) {
    config.header = {
      ...config.header,
      'X-Open-ID': openId
    };
  }
  
  return config;
};

// 响应拦截器
const responseInterceptor = (response) => {
  const { data } = response;
  // 处理token过期
  if (data.code === 401) {
    handleLoginExpired();
    return Promise.reject(new Error('登录已过期，请重新登录'));
  }
  
  return data;
};

// 刷新token
const refreshToken = () => {
  return new Promise((resolve, reject) => {
    const refreshToken = wx.getStorageSync('refreshToken');
    if (!refreshToken) {
      reject(new Error('未找到刷新token'));
      return;
    }

    wx.request({
      url: `${BASE_URL}/blade-auth/token`,
      method: 'POST',
      data: {
        grantType: 'refresh_token',
        refreshToken: refreshToken
      },
      success: (res) => {
        if (res.data.success) {
          const { accessToken, refreshToken } = res.data.data;
          wx.setStorageSync('token', accessToken);
          wx.setStorageSync('refreshToken', refreshToken);
          resolve(accessToken);
        } else {
          reject(new Error(res.data.msg));
        }
      },
      fail: reject
    });
  });
};

// 统一请求方法
const request = (options) => {
  const config = requestInterceptor(options);
  console.log(config);  
  // accessToken: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aM2NpDQsCGbjX-KXssWkyHv4S6WRWhKwBM3rpuy3pOk"

  return new Promise((resolve, reject) => {
    wx.request({
      ...config,
      url: `${BASE_URL}${config.url}`,
        success: (res) => {
        resolve(responseInterceptor(res));
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

module.exports = {
  request,
  refreshToken
}; 