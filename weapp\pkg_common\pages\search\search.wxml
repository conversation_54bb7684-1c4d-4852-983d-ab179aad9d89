<view class="container">
  <!-- 搜索框 -->
  <view class="search-header">
    <view class="search-bar">
      <image class="search-icon" src="/assets/images/home/<USER>" mode="aspectFit"/>
      <input 
        class="search-input" 
        placeholder="输入关键词搜索帖子" 
        placeholder-class="placeholder"
        value="{{searchText}}"
        bindinput="onSearchInput"
        focus="{{true}}"
        confirm-type="search"
        bindconfirm="onSearch"
      />
      <view class="clear-btn" bindtap="clearSearch" wx:if="{{searchText}}">
        <image class="clear-icon" src="/assets/images/common/clear.png" mode="aspectFit"/>
      </view>
    </view>
    <view class="cancel-btn" bindtap="onCancel">取消</view>
  </view>

  <!-- AI助手 -->
  <view class="ai-helper" bindtap="onTapAI">
    <image class="ai-icon" src="/assets/images/search/robot.png" mode="aspectFit"/>
    <text class="ai-text">试试AI搜索，找到更精准的内容</text>
  </view>

  <!-- 分类标签 -->
  <!-- <view class="category-section">
    <view class="category-list">
      <view 
        class="category-item {{activeCategory === item ? 'active' : ''}}" 
        wx:for="{{categories}}" 
        wx:key="*this"
        bindtap="onTapCategory"
        data-category="{{item}}"
      >
        {{item}}
      </view>
    </view>
  </view> -->

  <!-- 推荐帖子 -->
  <view class="recommend-section">
    <view class="section-title">推荐</view>
    <view class="post-list">
      <content-card
        wx:for="{{posts}}"
        wx:key="id"
        post="{{item}}"
        themeColor="#F18F01"
      />
    </view>
  </view>
</view> 