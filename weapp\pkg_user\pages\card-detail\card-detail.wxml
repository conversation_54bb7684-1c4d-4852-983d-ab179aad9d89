<!-- pkg_user/pages/card-detail/card-detail.wxml -->
<view class="card-detail-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载名片...</text>
  </view>

  <!-- 名片内容 -->
  <view wx:else class="card-content">
    <!-- 名片主体 -->
    <view class="business-card">
      <!-- 名片背景装饰 -->
      <view class="card-decoration">
        <view class="decoration-circle circle-1"></view>
        <view class="decoration-circle circle-2"></view>
        <view class="decoration-line line-1"></view>
        <view class="decoration-line line-2"></view>
      </view>

      <!-- 名片正面 -->
      <view class="card-front">
        <!-- 头像区域 -->
        <view class="avatar-section">
          <view class="avatar-container">
            <image 
              wx:if="{{cardInfo.avatar}}" 
              src="{{cardInfo.avatar}}" 
              class="avatar-image"
              mode="aspectFill"
            />
            <view wx:else class="avatar-placeholder">
              {{cardInfo.name.charAt(0)}}
            </view>
          </view>
        </view>

        <!-- 基本信息区域 -->
        <view class="info-section">
          <view class="name-area">
            <text class="card-name">{{cardInfo.name}}</text>
            <text class="card-position">{{cardInfo.position}}</text>
          </view>
          
          <view class="company-area">
            <text class="card-company">{{cardInfo.company}}</text>
          </view>
        </view>

        <!-- 联系方式区域 -->
        <view class="contact-section">
          <view class="contact-item" wx:if="{{cardInfo.phone}}" bindtap="onCallPhone">
            <text class="contact-icon">📱</text>
            <text class="contact-text">{{cardInfo.phone}}</text>
          </view>
          
          <view class="contact-item" wx:if="{{cardInfo.email}}" bindtap="onSendEmail">
            <text class="contact-icon">📧</text>
            <text class="contact-text">{{cardInfo.email}}</text>
          </view>
          
          <view class="contact-item" wx:if="{{cardInfo.wechat}}" bindtap="onCopyWechat">
            <text class="contact-icon">💬</text>
            <text class="contact-text">{{cardInfo.wechat}}</text>
          </view>
          
          <view class="contact-item" wx:if="{{cardInfo.website}}" bindtap="onViewWebsite">
            <text class="contact-icon">🌐</text>
            <text class="contact-text">{{cardInfo.website}}</text>
          </view>
        </view>

        <!-- 地址信息 -->
        <view class="address-section" wx:if="{{cardInfo.address}}">
          <view class="address-item" bindtap="onViewAddress">
            <text class="address-icon">📍</text>
            <text class="address-text">{{cardInfo.address}}</text>
          </view>
        </view>

        <!-- 备注信息 -->
        <view class="remark-section" wx:if="{{cardInfo.remark}}">
          <text class="remark-label">备注：</text>
          <text class="remark-text">{{cardInfo.remark}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-section">
      <view class="action-row">
        <view class="action-btn primary" bindtap="onCallPhone" wx:if="{{cardInfo.phone}}">
          <text class="action-icon">📞</text>
          <text class="action-text">拨打电话</text>
        </view>
        
        <view class="action-btn secondary" bindtap="onSaveContact">
          <text class="action-icon">👤</text>
          <text class="action-text">存通讯录</text>
        </view>
      </view>
      
      <view class="action-row">
        <view class="action-btn secondary" bindtap="onShareCard">
          <text class="action-icon">📤</text>
          <text class="action-text">分享名片</text>
        </view>
        
        <view class="action-btn secondary" bindtap="onEditCard" wx:if="{{!isPreview}}">
          <text class="action-icon">✏️</text>
          <text class="action-text">编辑名片</text>
        </view>
        
        <view class="action-btn primary" bindtap="onEditCard" wx:if="{{isPreview}}">
          <text class="action-icon">✅</text>
          <text class="action-text">确认保存</text>
        </view>
      </view>
    </view>

    <!-- 创建时间 -->
    <view class="meta-info" wx:if="{{cardInfo.createTime && !isPreview}}">
      <text class="meta-text">创建时间：{{cardInfo.createTime}}</text>
    </view>
  </view>
</view>
