/* 页面整体 */
page {
  height: 100vh;
  background: #f5f5f5;
}

/* 滚动容器 */
.scroll-container {
  padding: 0 20rpx;
  position: relative;
  background: #f5f5f5;
  box-sizing: border-box;
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 1;
  padding: 50rpx 20rpx 0 20rpx;
  width: 100%;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 更多图标 */
.more-icon {
  position: fixed;
  top: 120rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.more-icon:active {
  transform: scale(0.9);
  background: rgba(0, 0, 0, 0.5);
}

.more-icon image {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1); /* 将图标改为白色 */
}

/* 更多功能弹窗 */
.more-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding-top: 180rpx;
  padding-right: 20rpx;
}

.more-content {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  overflow: hidden;
  min-width: 200rpx;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.more-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.more-item:last-child {
  border-bottom: none;
}

.more-item:active {
  background: #f5f5f5;
}

.more-icon-item {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
}

.more-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 帖子详情 */
.post-detail {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 帖子头部 */
.post-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-details {
  flex: 1;
}

.nickname {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.post-time {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.post-time .icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.tag {
  padding: 8rpx 16rpx;
  background: #ff6b6b;
  color: #fff;
  font-size: 24rpx;
  border-radius: 20rpx;
  white-space: nowrap;
}

/* 帖子标题 */
.post-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

/* 帖子内容 */
.post-content {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

/* 地址信息 */
.post-address {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.post-address .icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
}

/* 图片区域 */
.post-images {
  margin-bottom: 30rpx;
}

.post-image {
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.post-image:last-child {
  margin-bottom: 0;
}

/* 互动统计 */
.interaction-stats {
  display: flex;
  justify-content: space-around;
  padding: 30rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 30rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
}

.stat-item .icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

/* 联系信息 */
.contact-info {
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 24rpx;
}

.contact-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.contact-item {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 28rpx;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-item .label {
  color: #666;
  width: 140rpx;
}

.contact-item .value {
  color: #333;
  flex: 1;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.error-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.error-text {
  color: #999;
  font-size: 28rpx;
}

/* 统一卡片风格 */


.contact-card-bg {
  background: #f8f8f8;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.06);
  margin-bottom: 20rpx;
  padding: 32rpx 24rpx;
}

.stats-bar {
  background: transparent;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
  margin-bottom: 22rpx;
  gap: 32rpx;
}

.comment-section {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 0;
}

.map-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.06);
  margin-bottom: 20rpx;
  padding: 32rpx 24rpx;
}

.post-content-card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.06);
  margin-bottom: 20rpx;
  padding: 32rpx 24rpx;
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}
.image-grid {
  margin-bottom: 20rpx;
}

.comment-section {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
  padding: 32rpx 24rpx 0 24rpx;
}

.post-content {
  color: #444;
  font-size: 30rpx;
  line-height: 1.7;
  margin-bottom: 0;
}

/* 底部操作栏吸底及主按钮主色调 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx 32rpx 24rpx;
  z-index: 100;
}
.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 0;
  padding: 0;
}
.action-icon {
  width: 44rpx;
  height: 44rpx;
  margin-bottom: 4rpx;
}
.action-item text {
  font-size: 24rpx;
  color: #888;
}
.contact-btn {
  flex: 2;
  background: linear-gradient(to right, #ff6b6b, #ff8585);
  color: #fff;
  text-align: center;
  padding: 24rpx 0;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-left: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(255,107,107,0.08);
  border: none;
}

/* 适配底部安全区 */
@media (max-width: 750rpx) {
  .bottom-actions {
    padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  }
}

/* 优化scroll-view内容底部留白，避免被底部操作栏遮挡 */
.main-content {
  padding-bottom: 160rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .post-detail {
    margin: 10rpx;
    padding: 20rpx;
  }
  
  .post-title {
    font-size: 32rpx;
  }
  
  .post-content {
    font-size: 28rpx;
  }
  
  .bottom-actions {
    padding: 15rpx 20rpx;
  }
  
  .action-item {
    margin-right: 40rpx;
  }
} 

.empty-feedback {
  text-align: center;
  color: #bbb;
  font-size: 28rpx;
  padding: 40rpx 24rpx 32rpx 24rpx;
} 

.contact-card-bg {
  background: #f8f8f8;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 0 0 0 0;
}
.stats-card {
  margin-bottom: 24rpx;
} 

.action-btns {
  display: flex;
  gap: 32rpx;
  justify-content: center;
  margin: 32rpx 0 24rpx 0;
}
.btn-primary {
  flex: 1;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8585 100%);
  color: #fff;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  padding: 20rpx 0;
  border: none;
  outline: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.2);
  transition: all 0.3s ease;
}
.btn-primary:active {
  background: linear-gradient(135deg, #e55555 0%, #ff6b6b 100%);
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}
.btn-outline {
  flex: 1;
  background: #fff;
  color: #ff6b6b;
  border: 2rpx solid #ff6b6b;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  padding: 20rpx 0;
  outline: none;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.08);
  transition: all 0.3s ease;
}
.btn-outline:active {
  color: #e55555;
  border-color: #e55555;
  background: #fff0f0;
  transform: translateY(1rpx);
}

/* 反馈标签弹窗样式 */
.feedback-dialog-mask {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.35);
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.feedback-dialog {
  background: #fff;
  border-radius: 18rpx;
  padding: 40rpx 32rpx 32rpx 32rpx;
  min-width: 540rpx;
  max-width: 90vw;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.12);
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 28rpx;
  text-align: center;
}
.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 18rpx;
  margin-bottom: 28rpx;
  justify-content: center;
}
.tag-item {
  padding: 12rpx 32rpx;
  border-radius: 32rpx;
  background: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  border: 2rpx solid #f5f5f5;
  transition: all 0.2s;
}
.tag-item.active {
  background: #ff6b6b;
  color: #fff;
  border-color: #ff6b6b;
}
.feedback-textarea {
  min-height: 90rpx;
  border-radius: 12rpx;
  border: 1rpx solid #eee;
  padding: 18rpx;
  font-size: 28rpx;
  margin-bottom: 28rpx;
  resize: none;
}
.dialog-actions {
  display: flex;
  gap: 32rpx;
  justify-content: center;
} 

.comment-btn,
.comment-btn:active,
.comment-btn:focus,
.comment-btn:visited {
  background: #ff6b6b;
  color: #fff;
  border: none;
}
.comment-btn::after {
  border: none;
}

/* 回复输入框样式 */
.reply-input-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  border-top: 1rpx solid #e0e0e0;
  z-index: 999;
  padding-bottom: env(safe-area-inset-bottom);
}

.reply-input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #f8f8f8;
}

.reply-target {
  font-size: 28rpx;
  color: #666;
}

.close-reply {
  width: 32rpx;
  height: 32rpx;
}

.reply-input-content {
  padding: 20rpx 30rpx;
}

.reply-textarea {
  width: 100%;
  min-height: 120rpx;
  max-height: 300rpx;
  font-size: 30rpx;
  line-height: 1.5;
  border: none;
  outline: none;
  resize: none;
}

.reply-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}

.char-count {
  font-size: 24rpx;
  color: #999;
}

.reply-submit-btn {
  background: #ff6b6b;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  min-width: 120rpx;
  position: relative;
  z-index: 10;
}

.reply-submit-btn::after {
  border: none;
}

.reply-submit-btn[disabled] {
  background: #ccc;
  color: #999;
}