// pages/local/local.js
const { getInstitutionList } = require('../../stores/institutionStore.js');
const { getInstitutionTypeList } = require('../../stores/institutionTypeStore.js');

Page({
  data: {
    // 机构列表数据
    institutionList: [],
    // 加载状态
    loading: true,
    // 是否还有更多数据
    hasMore: true,
    // 当前页码
    currentPage: 1,
    // 每页数量
    pageSize: 10,
    // 搜索关键词
    searchKeyword: '',
    // 分类筛选
    selectedCategory: 'all',
    selectedCategoryId: null, // 用于存储真实的分类ID
    categories: [], // 从接口获取的分类数据
    // 地区信息
    currentLocation: '当前位置',
    // 刷新状态（已禁用）
    refreshing: false,

    // 导航栏相关
    navBarHeight: 88,
    showTabbar: true,
    showStickyCategory: false,
    scrollIntoView: '',

    // 滚动加载优化相关
    scrollHeight: 0,
    scrollViewHeight: 0,
    preloadThreshold: 0.5,  // 预加载阈值（滚动到50%时开始加载）
    isPreloading: false,    // 是否正在预加载
    reachedBottom: false,   // 是否已到达底部

    // 刷新动画相关（已移除）
    // coins: [],

    // 主Tab切换（最新/附近）
    currentTab: 0, // 0: 最新, 1: 附近

    // 首页组件数据
    banners: [],
    quickAccess: [],

    // 页面加载状态标志
    isPageLoaded: false,
    lastScrollTop: 0
  },

  onLoad(options) {
    console.log('本地页面加载');
    this.initPage();
  },

  onShow() {
    // 显示底部导航栏
    this.setData({ showTabbar: true });

    // 只有在页面已经加载过的情况下才刷新数据（从其他页面返回时）
    if (this.data.isPageLoaded) {
      console.log('从其他页面返回，刷新数据');
      this.refreshData();
    }
  },

  onHide() {
    // 页面隐藏时隐藏底部导航栏
    this.setData({ showTabbar: false });
  },

  // 导航栏准备完成
  onNavReady(e) {
    const navHeight = e.detail.height;
    this.setData({ navBarHeight: navHeight });
    this.customNav = this.selectComponent('#custom-nav');

    // 获取滚动容器高度
    this.getScrollViewHeight();
  },

  // 显示抽屉
  onShowDrawer() {
    console.log('显示抽屉');
  },

  // 显示地区选择器
  onRegionPickerShow() {
    console.log('显示地区选择器');
  },

  // 初始化页面
  async initPage() {
    try {
      // 并行加载基础数据
      await Promise.all([
        this.loadInstitutionTypes(), // 加载机构分类
        this.getCurrentLocation()     // 获取用户位置信息
      ]);

      // 加载机构列表
      await this.loadInstitutionList();

      // 标记页面已加载
      this.setData({ isPageLoaded: true });
    } catch (error) {
      console.error('页面初始化失败:', error);
      this.setData({
        loading: false,
        isPageLoaded: true
      });
    }
  },

  // 加载机构分类
  async loadInstitutionTypes() {
    try {
      const categories = await getInstitutionTypeList();
      this.setData({ categories });
      console.log('机构分类加载完成:', categories.length);
    } catch (error) {
      console.error('加载机构分类失败:', error);
      // 使用默认分类数据
      this.setData({
        categories: [
          { id: 'all', name: '全部', icon: '/assets/images/institution-types/all.png' },
          { id: 'education', name: '教育培训', icon: '/assets/images/institution-types/education.png' },
          { id: 'medical', name: '医疗健康', icon: '/assets/images/institution-types/medical.png' },
          { id: 'finance', name: '金融服务', icon: '/assets/images/institution-types/finance.png' },
          { id: 'government', name: '政府机构', icon: '/assets/images/institution-types/government.png' },
          { id: 'business', name: '商业服务', icon: '/assets/images/institution-types/business.png' }
        ]
      });
    }
  },

  // 获取当前位置
  async getCurrentLocation() {
    try {
      const location = await this.getLocation();
      // 这里可以调用地理编码API获取地址信息
      this.setData({
        currentLocation: '当前位置' // 实际项目中应该是具体地址
      });
    } catch (error) {
      console.log('获取位置失败:', error);
      this.setData({
        currentLocation: '位置获取失败'
      });
    }
  },

  // 获取位置信息
  getLocation() {
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'gcj02',
        success: resolve,
        fail: reject
      });
    });
  },



  // 加载机构列表
  async loadInstitutionList(isRefresh = false) {
    if (this.data.loading && !isRefresh) return;

    try {
      this.setData({
        loading: true,
        refreshing: isRefresh,
        isPreloading: false // 重置预加载状态
      });

      // 构建请求参数
      const params = {
        current: isRefresh ? 1 : this.data.currentPage,
        size: this.data.pageSize,
        sortType: this.data.currentTab === 0 ? 'latest' : 'nearby'
      };

      // 添加分类筛选参数
      if (this.data.selectedCategory !== 'all' && this.data.selectedCategoryId) {
        params.typeId = this.data.selectedCategoryId;
      }

      // 添加搜索关键词
      if (this.data.searchKeyword) {
        params.name = this.data.searchKeyword;
      }

      console.log('请求参数:', params);

      // 调用store方法获取数据
      const result = await getInstitutionList(params);

      const newList = result.records || [];
      const institutionList = isRefresh ? newList : [...this.data.institutionList, ...newList];

      // 计算是否还有更多数据
      const hasMore = result.current < result.pages;

      this.setData({
        institutionList: institutionList,
        hasMore: hasMore,
        currentPage: isRefresh ? 2 : this.data.currentPage + 1,
        loading: false,
        refreshing: false,
        isPreloading: false,
        reachedBottom: !hasMore
      });

      console.log('机构列表加载完成:', {
        total: institutionList.length,
        current: result.current,
        pages: result.pages,
        hasMore: hasMore
      });
    } catch (error) {
      console.error('加载机构列表失败:', error);
      this.setData({
        loading: false,
        refreshing: false,
        isPreloading: false
      });

      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 刷新数据
  async refreshData() {
    // 重置状态
    this.setData({
      currentPage: 1,
      hasMore: true,
      reachedBottom: false,
      isPreloading: false
    });
    await this.loadInstitutionList(true);
  },

  // 加载更多
  async loadMore() {
    if (!this.data.hasMore || this.data.loading) return;
    await this.loadInstitutionList();
  },

  // 搜索
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  onSearch() {
    this.setData({
      currentPage: 1,
      institutionList: [],
      reachedBottom: false,
      hasMore: true,
      isPreloading: false
    });
    this.loadInstitutionList(true);
  },

  // 主Tab切换（最新/附近）
  switchMainTab(e) {
    const index = e.currentTarget.dataset.index;
    if (index === this.data.currentTab) return;

    this.setData({
      currentTab: index,
      currentPage: 1,
      institutionList: [],
      reachedBottom: false,
      hasMore: true,
      isPreloading: false
    });

    // 重新加载数据
    this.loadInstitutionList(true);
  },

  // 分类筛选
  switchTab(e) {
    const index = e.currentTarget.dataset.index;
    const category = this.data.categories[index];

    if (category.id === this.data.selectedCategory) return;

    // 设置选中的分类
    const selectedCategoryId = category.id === 'all' ? null : category.id;

    this.setData({
      selectedCategory: category.id,
      selectedCategoryId: selectedCategoryId,
      currentPage: 1,
      institutionList: [],
      reachedBottom: false,
      hasMore: true,
      isPreloading: false
    });

    console.log('切换分类:', {
      categoryName: category.name,
      categoryId: category.id,
      selectedCategoryId: selectedCategoryId
    });

    this.loadInstitutionList(true);
  },

  // 点击机构项
  onInstitutionTap(e) {
    const institution = e.detail.institution;
    console.log('点击机构:', institution);

    // 跳转到机构详情页
    wx.navigateTo({
      url: `/pkg_user/pages/institution-detail/institution-detail?id=${institution.id}`
    });
  },

  // 申请入驻点击
  onApplySettle() {
    console.log('申请入驻点击');

    // 检查登录状态
    if (!this.checkLogin()) {
      return;
    }

    // 跳转到申请入驻页面
    wx.navigateTo({
      url: '/pkg_user/pages/apply-settle/apply-settle',
      fail: () => {
        // 如果页面不存在，显示开发中提示
        wx.showModal({
          title: '申请入驻',
          content: '感谢您的关注！申请入驻功能正在开发中，请稍后再试。\n\n如有紧急需求，请联系客服。',
          showCancel: true,
          cancelText: '知道了',
          confirmText: '联系客服',
          success: (res) => {
            if (res.confirm) {
              // 这里可以跳转到客服页面或拨打客服电话
              wx.makePhoneCall({
                phoneNumber: '************',
                fail: () => {
                  wx.showToast({
                    title: '请手动拨打************',
                    icon: 'none',
                    duration: 3000
                  });
                }
              });
            }
          }
        });
      }
    });
  },

  // 检查登录状态
  checkLogin() {
    // 这里应该检查实际的登录状态
    // 暂时返回true，实际项目中需要实现登录检查逻辑
    return true;
  },

  // 拨打电话
  onCallPhone(e) {
    e.stopPropagation();
    const phone = e.currentTarget.dataset.phone;
    if (phone) {
      wx.makePhoneCall({
        phoneNumber: phone
      });
    }
  },

  // 查看位置
  onViewLocation(e) {
    e.stopPropagation();
    const item = e.currentTarget.dataset.item;
    if (item.latitude && item.longitude) {
      wx.openLocation({
        latitude: parseFloat(item.latitude),
        longitude: parseFloat(item.longitude),
        name: item.name,
        address: item.address
      });
    }
  },

  // 监听页面滚动
  onScroll(e) {
    const scrollTop = e.detail.scrollTop;
    // 分类栏吸顶
    wx.createSelectorQuery().select('#category-section').boundingClientRect(rect => {
      if (rect && rect.top <= this.data.navBarHeight) {
        if (!this.data.showStickyCategory) this.setData({ showStickyCategory: true });
      } else {
        if (this.data.showStickyCategory) this.setData({ showStickyCategory: false });
      }
    }).exec();
    // 底部tabbar隐藏动画
    const tabbar = this.selectComponent('#custom-nav');
    if (!tabbar) return;
    if (!this.lastScrollTop) this.lastScrollTop = 0;
    const delta = scrollTop - this.lastScrollTop;
    if (delta > 10 && scrollTop > 50) {
      tabbar.setHide(true);
    } else if (delta < -10) {
      tabbar.setHide(false);
    }
    this.lastScrollTop = scrollTop;
  },

  // 下拉刷新（已禁用）
  onPullDownRefresh() {
    // 下拉刷新已禁用
    console.log('下拉刷新已禁用');
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.loading || !this.data.hasMore || this.data.reachedBottom) {
      console.log('到达底部，停止加载');
      return;
    }

    // 如果预加载还没完成，等待预加载
    if (this.data.isPreloading) {
      console.log('预加载进行中，跳过onReachBottom');
      return;
    }

    console.log('onReachBottom触发加载');
    this.loadMore();
  },

  // 获取滚动容器高度
  getScrollViewHeight() {
    const query = wx.createSelectorQuery().in(this);
    query.select('.main-scroll-view').boundingClientRect((rect) => {
      if (rect) {
        this.setData({ scrollViewHeight: rect.height });
        console.log('滚动容器高度:', rect.height);
      }
    }).exec();
  },

  // 智能预加载处理
  handleSmartPreload(scrollTop, scrollHeight, scrollViewHeight) {
    // 如果没有更多数据或正在加载，直接返回
    if (!this.data.hasMore || this.data.loading || this.data.isPreloading) {
      return;
    }

    // 计算滚动进度
    const scrollProgress = scrollTop / (scrollHeight - scrollViewHeight);

    // 检查是否到达底部
    const bottomThreshold = 50; // 距离底部50px
    const distanceToBottom = scrollHeight - scrollTop - scrollViewHeight;

    if (distanceToBottom <= bottomThreshold) {
      this.setData({ reachedBottom: true });
      console.log('已到达底部，停止滚动');
      return;
    }

    // 当滚动进度超过预加载阈值时，开始预加载
    if (scrollProgress >= this.data.preloadThreshold && !this.data.isPreloading) {
      console.log(`滚动进度: ${(scrollProgress * 100).toFixed(1)}%, 开始预加载下一页`);
      this.preloadNextPage();
    }
  },

  // 预加载下一页数据
  async preloadNextPage() {
    if (this.data.loading || !this.data.hasMore || this.data.isPreloading) {
      return;
    }

    try {
      this.setData({ isPreloading: true });
      const nextPage = this.data.currentPage + 1;
      this.setData({ currentPage: nextPage });

      // 加载数据
      await this.loadInstitutionList();

      console.log(`预加载完成，当前页码: ${nextPage}`);
    } catch (error) {
      console.error('预加载失败:', error);
      // 预加载失败时回退页码
      this.setData({ currentPage: this.data.currentPage - 1 });
    } finally {
      this.setData({ isPreloading: false });
    }
  },

  // 生成刷新动画金币（已移除）
  // generateCoins() {
  //   // 下拉刷新动画已移除
  // }
});
