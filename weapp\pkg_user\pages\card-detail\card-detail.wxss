/* pkg_user/pages/card-detail/card-detail.wxss */

.card-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #fff;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 名片内容 */
.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 名片主体 */
.business-card {
  width: 680rpx;
  height: 420rpx;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border-radius: 24rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
  margin-bottom: 60rpx;
}

/* 名片装饰 */
.card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  opacity: 0.1;
}

.circle-1 {
  width: 120rpx;
  height: 120rpx;
  top: -60rpx;
  right: -60rpx;
}

.circle-2 {
  width: 80rpx;
  height: 80rpx;
  bottom: 20rpx;
  left: 20rpx;
}

.decoration-line {
  position: absolute;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  opacity: 0.1;
}

.line-1 {
  width: 200rpx;
  height: 4rpx;
  top: 60rpx;
  right: 40rpx;
  transform: rotate(15deg);
}

.line-2 {
  width: 150rpx;
  height: 3rpx;
  bottom: 80rpx;
  left: 40rpx;
  transform: rotate(-10deg);
}

/* 名片正面 */
.card-front {
  padding: 40rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

/* 头像区域 */
.avatar-section {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20rpx;
}

.avatar-container {
  width: 100rpx;
  height: 100rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  border: 4rpx solid #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 基本信息区域 */
.info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.name-area {
  margin-bottom: 16rpx;
}

.card-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.card-position {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.company-area {
  margin-bottom: 24rpx;
}

.card-company {
  font-size: 32rpx;
  color: #ff6b6b;
  font-weight: 600;
}

/* 联系方式区域 */
.contact-section {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 8rpx 0;
  transition: background 0.2s ease;
  border-radius: 8rpx;
}

.contact-item:active {
  background: rgba(255, 107, 107, 0.1);
}

.contact-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
  width: 32rpx;
}

.contact-text {
  font-size: 24rpx;
  color: #555;
  flex: 1;
}

/* 地址区域 */
.address-section {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #eee;
}

.address-item {
  display: flex;
  align-items: flex-start;
  padding: 8rpx 0;
  transition: background 0.2s ease;
  border-radius: 8rpx;
}

.address-item:active {
  background: rgba(255, 107, 107, 0.1);
}

.address-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-top: 2rpx;
}

.address-text {
  font-size: 24rpx;
  color: #555;
  line-height: 1.4;
  flex: 1;
}

/* 备注区域 */
.remark-section {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #eee;
}

.remark-label {
  font-size: 22rpx;
  color: #999;
  margin-right: 8rpx;
}

.remark-text {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

/* 操作按钮区域 */
.action-section {
  width: 680rpx;
  margin-bottom: 40rpx;
}

.action-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-row:last-child {
  margin-bottom: 0;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
}

.action-btn.secondary {
  background: #fff;
  color: #333;
}

.action-icon {
  font-size: 24rpx;
  margin-bottom: 4rpx;
}

.action-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 元信息 */
.meta-info {
  text-align: center;
  margin-top: 20rpx;
}

.meta-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
