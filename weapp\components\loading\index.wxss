.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
}

/* 旋转加载动画 */
.loading-spinner {
  position: relative;
  width: 60rpx;
  height: 60rpx;
}

.loading-spinner-item {
  position: absolute;
  width: 6rpx;
  height: 20rpx;
  border-radius: 3rpx;
  background-color: #ff6b6b;
  opacity: 0.2;
  transform-origin: center 30rpx;
  animation: spinner 1.2s linear infinite;
}

.loading-spinner-item:nth-child(1) { transform: rotate(0deg); animation-delay: -1.1s; }
.loading-spinner-item:nth-child(2) { transform: rotate(30deg); animation-delay: -1s; }
.loading-spinner-item:nth-child(3) { transform: rotate(60deg); animation-delay: -0.9s; }
.loading-spinner-item:nth-child(4) { transform: rotate(90deg); animation-delay: -0.8s; }
.loading-spinner-item:nth-child(5) { transform: rotate(120deg); animation-delay: -0.7s; }
.loading-spinner-item:nth-child(6) { transform: rotate(150deg); animation-delay: -0.6s; }
.loading-spinner-item:nth-child(7) { transform: rotate(180deg); animation-delay: -0.5s; }
.loading-spinner-item:nth-child(8) { transform: rotate(210deg); animation-delay: -0.4s; }
.loading-spinner-item:nth-child(9) { transform: rotate(240deg); animation-delay: -0.3s; }
.loading-spinner-item:nth-child(10) { transform: rotate(270deg); animation-delay: -0.2s; }
.loading-spinner-item:nth-child(11) { transform: rotate(300deg); animation-delay: -0.1s; }
.loading-spinner-item:nth-child(12) { transform: rotate(330deg); animation-delay: 0s; }

@keyframes spinner {
  0% { opacity: 1; }
  100% { opacity: 0.2; }
}

/* 点点加载动画 */
.loading-dots {
  display: flex;
  align-items: center;
}

.loading-dots-item {
  width: 16rpx;
  height: 16rpx;
  margin: 0 8rpx;
  background-color: #ff6b6b;
  border-radius: 50%;
  animation: dots 1.4s infinite ease-in-out;
}

.loading-dots-item:nth-child(1) { animation-delay: -0.32s; }
.loading-dots-item:nth-child(2) { animation-delay: -0.16s; }

@keyframes dots {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
} 