// ai.js
Page({
  data: {
    newsList: [
      { content: '中俄决定永久睦邻友好，关系不受第三方干扰' },
      { content: 'TREASURE盒首发消息引关注' },
      { content: '十四届全国人大三次会议外交主题记者会' },
      { content: '王毅重申台湾唯一称谓是中国台湾省' },
      { content: '国家卫健委起草育儿补贴方案' }
    ],
    statusBarHeight: 0,
    menuButtonHeight: 0,
    menuButtonTop: 0,
    navHeight: 0,
    inputValue: '',
    isVoiceMode: false,
    recordState: 'ready', // ready, recording, cancel
    recordStartTime: 0,
    recordingTip: '按住说话'
  },

  onLoad: function() {
    // 获取状态栏和胶囊按钮的位置信息
    const systemInfo = wx.getSystemInfoSync();
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      menuButtonHeight: menuButtonInfo.height,
      menuButtonTop: menuButtonInfo.top,
      navHeight: menuButtonInfo.height + (menuButtonInfo.top - systemInfo.statusBarHeight) * 2
    });

    // 初始化录音管理器
    this.initRecorder();
  },

  // 初始化录音管理器
  initRecorder: function() {
    this.recorderManager = wx.getRecorderManager();
    
    this.recorderManager.onStart(() => {
      console.log('录音开始');
      this.setData({
        recordStartTime: Date.now(),
        recordingTip: '松开发送',
        recordState: 'recording'
      });
    });
    
    this.recorderManager.onStop((res) => {
      if (this.data.recordState === 'recording') {
        const duration = Date.now() - this.data.recordStartTime;
        
        // 如果录音时间太短
        if (duration < 1000) {
          wx.showToast({
            title: '说话时间太短',
            icon: 'none',
            duration: 1500
          });
          return;
        }

        // 如果录音时间超过60秒
        if (duration > 60000) {
          wx.showToast({
            title: '录音时间不能超过60秒',
            icon: 'none',
            duration: 1500
          });
          return;
        }

        // 显示识别中的提示
        wx.showLoading({
          title: '正在识别...',
          mask: true
        });

        // 调用语音识别
        this.recognizeSpeech(res.tempFilePath);
      }
      
      this.setData({
        recordState: 'ready',
        recordingTip: '按住说话'
      });
    });

    // 录音错误事件
    this.recorderManager.onError((err) => {
      console.error('录音错误：', err);
      wx.showToast({
        title: this.getRecordErrorMessage(err.errMsg),
        icon: 'none',
        duration: 2000
      });
      this.setData({
        recordState: 'ready',
        recordingTip: '按住说话'
      });
    });
  },
  // 获取录音错误信息
  getRecordErrorMessage: function(errMsg) {
    const errorMap = {
      'operateRecorder:fail auth deny': '请授权录音权限',
      'operateRecorder:fail system permission denied': '系统录音权限被禁止',
      'operateRecorder:fail cannot find valid audio device': '找不到有效的录音设备',
      'operateRecorder:fail': '录音失败，请重试',
    };
    return errorMap[errMsg] || '录音出错，请重试';
  },
  // 语音识别
  recognizeSpeech: function(tempFilePath) {
    console.log('开始上传音频文件：', tempFilePath);
    
    wx.showLoading({
      title: '正在处理...',
      mask: true
    });

    // 上传到云存储
    const fileName = `audio_${Date.now()}.mp3`;
    wx.cloud.uploadFile({
      cloudPath: fileName,
      filePath: tempFilePath,
      success: res => {
        console.log('上传成功', res);
        // 获取文件访问链接
        wx.cloud.getTempFileURL({
          fileList: [res.fileID],
          success: urlRes => {
            console.log('获取链接成功', urlRes);
            const fileUrl = urlRes.fileList[0].tempFileURL;
            
            // 调用语音识别服务
            const params = {
              Action: 'SentenceRecognitionWX',
              EngSerViceType: '16k_zh',
              VoiceFormat: 'mp3',
              UsrAudioKey: fileName,
              SourceType: 0,
              Url: encodeURIComponent(fileUrl)
            };
            console.log('调用语音识别服务', params);

            wx.serviceMarket.invokeService({
              service: 'wxa8386175898e12c9',
              api: 'SentenceASR',
              data: params,
              success: (result) => {
                console.log('识别结果：', result);
                if (result.data && result.data.Response) {
                  if (result.data.Response.Error) {
                    console.error('识别错误：', result.data.Response.Error);
                    wx.showToast({
                      title: result.data.Response.Error.Message || '识别失败',
                      icon: 'none',
                      duration: 2000
                    });
                    return;
                  }
                  
                  if (result.data.Response.Result) {
                    const recognizedText = result.data.Response.Result.trim();
                    if (recognizedText) {
                      console.log('识别文本：', recognizedText);
                      this.setData({
                        inputValue: recognizedText
                      });
                      // 自动发送识别结果
                      this.onSendTap();
                    } else {
                      wx.showToast({
                        title: '未能识别到有效内容',
                        icon: 'none',
                        duration: 1500
                      });
                    }
                  } else {
                    console.log('返回数据中没有识别结果');
                    wx.showToast({
                      title: '未能识别到语音内容',
                      icon: 'none',
                      duration: 1500
                    });
                  }
                }
              },
              fail: (err) => {
                console.error('识别请求失败：', err);
                this.setData({
                  inputValue: "你好识别失败，测试数据"
                });
                // 自动发送识别结果
                this.onSendTap();
                wx.showToast({
                  title: this.getServiceErrorMessage(err),
                  icon: 'none',
                  duration: 2000
                });
              },
              complete: () => {
                wx.hideLoading();
                // 删除云存储中的临时文件
                wx.cloud.deleteFile({
                  fileList: [res.fileID],
                  success: delRes => {
                    console.log('临时文件删除成功');
                  },
                  fail: delErr => {
                    console.error('临时文件删除失败', delErr);
                  }
                });
              }
            });
          },
          fail: urlErr => {
            console.error('获取链接失败', urlErr);
            wx.hideLoading();
            wx.showToast({
              title: '获取语音文件链接失败',
              icon: 'none'
            });
          }
        });
      },
      fail: err => {
        console.error('上传失败', err);
        wx.hideLoading();
        wx.showToast({
          title: '上传语音文件失败',
          icon: 'none'
        });
      }
    });
  },

  // 获取服务错误信息
  getServiceErrorMessage: function(err) {
    const errorMap = {
      '-1': '参数错误',
      '-2': '服务调用失败',
      '-3': '识别失败',
      '-6': '服务未开通',
      '-7': 'API配置错误',
      '-8': 'API未找到',
      '-10': '余额不足',
      '-11': '请求过于频繁'
    };
    return errorMap[err.errCode] || err.errMsg || '识别失败，请重试';
  },

  // 切换输入模式
  toggleInputMode: function() {
    // 请求录音权限
    if (!this.data.isVoiceMode) {
      wx.authorize({
        scope: 'scope.record',
        success: () => {
          this.setData({
            isVoiceMode: true
          });
        },
        fail: () => {
          wx.showModal({
            title: '提示',
            content: '需要您的录音权限，是否去设置？',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting();
              }
            }
          });
        }
      });
    } else {
      this.setData({
        isVoiceMode: false
      });
    }
  },

  // 开始录音
  startVoiceRecord: function(e) {
    this.setData({
      recordState: 'recording'
    });
    
    // 开始录音
    this.recorderManager.start({
      duration: 60000,
      sampleRate: 16000,
      numberOfChannels: 1,
      encodeBitRate: 96000,
      format: 'wav',
      frameSize: 3.2
    });

    // 震动反馈
    wx.vibrateShort();
  },

  // 结束录音
  endVoiceRecord: function() {
    if (this.data.recordState === 'recording') {
      this.recorderManager.stop();
    }
  },

  // 移动手指
  moveVoiceRecord: function(e) {
    if (this.data.recordState === 'recording') {
      // 判断是否移出录音区域，可以添加上划取消录音的逻辑
      const touchY = e.touches[0].clientY;
      const cancelY = 100; // 设定一个上划取消的阈值
      
      if (touchY < cancelY && this.data.recordState !== 'cancel') {
        this.setData({
          recordState: 'cancel',
          recordingTip: '松开手指，取消发送'
        });
        wx.showToast({
          title: '上划取消发送',
          icon: 'none'
        });
      } else if (touchY >= cancelY && this.data.recordState === 'cancel') {
        this.setData({
          recordState: 'recording',
          recordingTip: '松开发送'
        });
      }
    }
  },

  // 输入框输入
  onInput: function(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  // 点击发送按钮
  onSendTap: function() {
    if (!this.data.inputValue.trim()) {
      return;
    }
    // 保存输入内容
    const question = this.data.inputValue;
    
    // 跳转到聊天页面并传递问题内容
    wx.navigateTo({
      url: `/pages/ai/chat/chat?question=${encodeURIComponent(question)}`
    });

    // 清空输入框
    this.setData({
      inputValue: ''
    });
  },

  // 点击加号按钮
  onPlusTap: function() {
    wx.showToast({
      title: '更多功能开发中',
      icon: 'none'
    });
  },

  // 跳转到聊天页面
  navigateToChat: function() {
    wx.navigateTo({
      url: '/pages/chat/chat'
    });
  },

  // 发现更多按钮点击
  onDiscoverMore: function() {
    wx.showToast({
      title: '发现更多功能开发中',
      icon: 'none'
    });
  },

  // 换一批按钮点击
  onRefresh: function() {
    wx.showToast({
      title: '换一批功能开发中',
      icon: 'none'
    });
  }
}) 