/* 评论卡片样式 */
.comment-card {
  padding: 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.comment-card:last-child {
  border-bottom: none;
}

/* 评论头部 */
.comment-header {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
}

.user-name-line {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 4rpx;
  flex-wrap: wrap;
}

.nickname {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.reply-to {
  font-size: 24rpx;
  color: #007aff;
  background: #f0f8ff;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.time {
  font-size: 24rpx;
  color: #999;
}

/* 右侧点赞区域 */
.like-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  padding: 8rpx;
  border-radius: 8rpx;
  transition: background-color 0.2s;
}

.like-area:active {
  background-color: #f5f5f5;
}

.like-icon {
  width: 40rpx;
  height: 40rpx;
}

.like-icon.helpful {
  filter: hue-rotate(0deg) saturate(1.2);
}

.like-count {
  font-size: 22rpx;
  color: #666;
  min-width: 20rpx;
  text-align: center;
}

.like-count.helpful {
  color: #ff6b6b;
  font-weight: 600;
}

/* 评论内容区域 */
.comment-content {
  margin-left: 96rpx; /* 对齐头像右侧 */
}

.reply-inline-row {
  position: relative;
  display: flex;
  align-items: flex-end;
}

.content-text {
  width: 100%;
  display: flex;
  align-items: flex-end;
}

.content-text rich-text {
  flex: 1;
  min-width: 0;
}

.inline-reply-btn {
  position: absolute;
  right: 0;
  bottom: 0;
  color: #ff6b6b;
  font-size: 26rpx;
  background: #fff0f0;
  border-radius: 8rpx;
  padding: 0 14rpx;
  height: 36rpx;
  line-height: 36rpx;
  margin-left: 8rpx;
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
}

.inline-reply-btn:active {
  background: #ffe6e6;
  border-color: #ffd6d6;
  transform: scale(0.98);
}

.comment-image {
  margin-bottom: 12rpx;
}

.comment-image image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
}

/* 展开回复区域 */
.expand-replies-section {
  margin: 16rpx 0 8rpx 96rpx; /* 对齐内容区域 */
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 0;
  cursor: pointer;
}

.expand-line {
  flex: 1;
  height: 1rpx;
  background: #e0e0e0;
}

.expand-content {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 20rpx;
  background: linear-gradient(135deg, #fff0f0 0%, #ffe6e6 100%);
  border: 1rpx solid #ffebeb;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.1);
  transition: all 0.3s ease;
}

.expand-replies-section:active .expand-content {
  background: linear-gradient(135deg, #ffe6e6 0%, #ffd6d6 100%);
  border-color: #ffd6d6;
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(255, 107, 107, 0.15);
}

.expand-arrow {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
  display: inline-block;
}

.expand-arrow.expanded {
  transform: rotate(180deg);
}

.expand-text {
  font-size: 26rpx;
  color: #ff6b6b;
  font-weight: 600;
}

.comment-rich-text {
  word-break: break-all;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 8rpx;
}


/* 回复列表样式 */
.replies-container {
  margin-top: 24rpx;
  background: #fafafa;
  border-radius: 12rpx;
  padding: 20rpx;
  border-left: 4rpx solid #ff6b6b;
}

.reply-item {
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-content {
  display: flex;
  gap: 16rpx;
}

.reply-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.reply-info {
  flex: 1;
}

.reply-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
}

.reply-nickname {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.reply-to-user {
  font-size: 24rpx;
  color: #007aff;
}

.reply-time {
  font-size: 22rpx;
  color: #999;
  margin-left: auto;
}

.reply-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 12rpx;
}

.reply-image {
  margin-bottom: 12rpx;
}

.reply-image image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
}

.reply-actions {
  display: flex;
  gap: 32rpx;
  margin-top: 12rpx;
}

.reply-action {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  transition: all 0.2s;
}

.reply-action:active {
  background: #e0e0e0;
}

.reply-action-text {
  font-size: 24rpx;
  color: #666;
}

.reply-action-text.liked {
  color: #ff6b6b;
}

/* 无回复状态 */
.no-replies {
  text-align: center;
  padding: 40rpx 20rpx;
}

.no-replies-text {
  font-size: 26rpx;
  color: #999;
}

/* 回复状态区域 */
.reply-status {
  margin-top: 16rpx;
  text-align: center;
}

/* 加载更多回复 */
.load-more-replies {
  display: inline-block;
  padding: 8rpx 0;
}

.load-more-text {
  font-size: 26rpx;
  color: #ff6b6b;
  padding: 10rpx 24rpx;
  background: linear-gradient(135deg, #fff0f0 0%, #ffe6e6 100%);
  border: 1rpx solid #ffebeb;
  border-radius: 20rpx;
  font-weight: 500;
  transition: all 0.2s;
}

.load-more-replies:active .load-more-text {
  background: linear-gradient(135deg, #ffe6e6 0%, #ffd6d6 100%);
  border-color: #ffd6d6;
  transform: scale(0.98);
}

/* 全部加载完成提示 */
.all-loaded-tip {
  display: inline-block;
  padding: 8rpx 0;
}

.all-loaded-text {
  font-size: 24rpx;
  color: #999;
  padding: 8rpx 20rpx;
  background: #f5f5f5;
  border-radius: 16rpx;
  font-weight: 400;
}