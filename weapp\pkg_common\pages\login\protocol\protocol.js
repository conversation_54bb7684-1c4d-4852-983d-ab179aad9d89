Page({
  data: {
    title: '',
    content: ''
  },
  onLoad(options) {
    let type = options.type || 'user';
    let title = type === 'privacy' ? '隐私政策' : '用户协议';
    let content = '';
    const mdContent = `# 小程序开发规范文档\n\n## 1. 引言\n\n为了提高团队协作效率，保证项目代码的规范性、可读性和可维护性，特制定此开发规范。所有项目成员都应严格遵守本规范。\n\n## 2. 核心原则\n\n- **高内聚，低耦合**：模块功能应单一明确，模块之间的依赖关系应尽可能简单。\n- **关注点分离**：视图（WXML/WXSS）、业务逻辑（JS）、数据状态（Stores）应清晰分离。\n- **组件化思维**：积极将可复用的 UI 和业务逻辑抽象成组件，提高开发效率。\n\n## 3. 目录结构规范\n\n清晰的目录结构是项目规范的基础。我们约定以下核心目录结构：\n\n...（内容省略，实际请完整粘贴）...`;
    if (type === 'privacy') {
      content = `<h3>隐私政策</h3><p>${mdContent.replace(/\n/g, '<br/>')}</p>`;
    } else {
      content = `<h3>用户协议</h3><p>${mdContent.replace(/\n/g, '<br/>')}</p>`;
    }
    this.setData({ title, content });
  }
}); 