<!-- pages/map/map.wxml -->
<view class="map-container">
  <!-- 地图组件 -->
  <map
    id="map"
    class="map"
    latitude="{{latitude}}"
    longitude="{{longitude}}"
    scale="{{scale}}"
    markers="{{markers}}"
    show-location="{{true}}"
    show-compass="{{true}}"
    enable-zoom="{{true}}"
    enable-scroll="{{true}}"
    enable-rotate="{{false}}"
    enable-overlooking="{{false}}"
    bindmarkertap="onMarkerTap"
    bindtap="onMapTap"
  >
    <!-- 地图控制按钮 -->
    <cover-view class="map-controls">
      <cover-view class="control-btn refresh-btn" bindtap="onRefresh">
        <cover-image class="control-icon" src="/assets/images/common/refresh.png"></cover-image>
      </cover-view>
      <cover-view class="control-btn location-btn" bindtap="onBackToUserLocation">
        <cover-image class="control-icon" src="/assets/images/common/location.png"></cover-image>
      </cover-view>
    </cover-view>

    <!-- 位置获取加载状态 -->
    <cover-view wx:if="{{locationLoading}}" class="map-loading location-loading">
      <cover-view class="loading-spinner"></cover-view>
      <cover-text class="loading-text">正在获取您的位置...</cover-text>
    </cover-view>

    <!-- 帖子加载状态 -->
    <cover-view wx:elif="{{loading}}" class="map-loading">
      <cover-view class="loading-spinner"></cover-view>
      <cover-text class="loading-text">加载附近帖子中...</cover-text>
    </cover-view>

    <!-- 地图中心指示器 -->
    <cover-view class="map-center-indicator">
      <cover-view class="center-dot"></cover-view>
      <cover-view class="center-circle"></cover-view>
    </cover-view>

    <!-- 搜索范围指示器 -->
    <cover-view wx:if="{{!loading && !locationLoading && showSearchInfo}}" class="search-info">
      <cover-text class="search-radius">搜索范围: {{searchRadiusText}}</cover-text>
      <cover-text class="post-count">找到 {{markers.length}} 个帖子</cover-text>
    </cover-view>

    <!-- 移动提示 -->
    <cover-view wx:if="{{!loading && !locationLoading && markers.length > 0}}" class="move-tip">
      <cover-text class="tip-text">拖拽地图查看其他区域</cover-text>
    </cover-view>
  </map>

  <!-- 帖子详情弹窗 -->
  <view wx:if="{{showPostDetail}}" class="post-detail-modal">
    <view class="modal-mask" bindtap="onClosePostDetail"></view>
    <view class="modal-content">
      <!-- 帖子头部信息 -->
      <view class="post-header">
        <view class="post-author">
          <image class="author-avatar" src="{{selectedPost.avatar}}" mode="aspectFill"></image>
          <view class="author-info">
            <text class="author-name">{{selectedPost.author}}</text>
            <text class="post-time">{{selectedPost.createTime}}</text>
          </view>
        </view>
        <view class="close-btn" bindtap="onClosePostDetail">
          <text class="close-icon">×</text>
        </view>
      </view>

      <!-- 帖子内容 -->
      <view class="post-content">
        <text class="post-text">{{selectedPost.content}}</text>
        
        <!-- 帖子图片 -->
        <view wx:if="{{selectedPost.images && selectedPost.images.length > 0}}" class="post-images">
          <image 
            wx:for="{{selectedPost.images}}" 
            wx:key="*this"
            class="post-image" 
            src="{{item}}" 
            mode="aspectFill">
          </image>
        </view>
      </view>

      <!-- 帖子统计信息 -->
      <view class="post-stats">
        <view class="stat-item">
          <image class="stat-icon like-icon {{selectedPost.isLiked ? 'liked' : ''}}" src="/assets/images/common/like.png" mode="aspectFit"></image>
          <text class="stat-text {{selectedPost.isLiked ? 'liked' : ''}}">{{selectedPost.likeCount}}</text>
        </view>
        <view class="stat-item">
          <image class="stat-icon" src="/assets/images/common/comment.png" mode="aspectFit"></image>
          <text class="stat-text">{{selectedPost.commentCount}}</text>
        </view>
        <view class="stat-item" wx:if="{{selectedPost.distance}}">
          <image class="stat-icon" src="/assets/images/common/location.png" mode="aspectFit"></image>
          <text class="stat-text">{{selectedPost.distance}}</text>
        </view>
        <view class="stat-item" wx:if="{{selectedPost.category}}">
          <text class="category-tag">{{selectedPost.category}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="post-actions">
        <view class="action-btn secondary" bindtap="onLikePost">
          <image class="action-icon" src="/assets/images/common/like.png" mode="aspectFit"></image>
          <text class="action-text">点赞</text>
        </view>
        <view class="action-btn primary" bindtap="onViewPostDetail">
          <text class="action-text">查看详情</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部信息栏 -->
  <view class="map-info" bindtap="showPostList">
    <view class="info-item">
      <image class="info-icon" src="/assets/images/common/marker.png" mode="aspectFit"></image>
      <text class="info-text">共找到 {{markers.length}} 个附近帖子</text>
    </view>
    <view class="info-tip">
      <text class="tip-text">点击查看帖子列表</text>
    </view>
  </view>

  <!-- 帖子列表弹窗 -->
  <view wx:if="{{showPostList}}" class="post-list-modal">
    <view class="modal-mask" bindtap="hidePostList"></view>
    <view class="modal-content">
      <!-- 弹窗头部 -->
      <view class="post-list-header">
        <text class="post-list-title">附近帖子 ({{postList.length}})</text>
        <view class="close-btn" bindtap="hidePostList">
          <text class="close-icon">×</text>
        </view>
      </view>

      <!-- 帖子列表 -->
      <scroll-view class="post-list-scroll" scroll-y>
        <block wx:if="{{postList.length > 0}}">
          <view 
            wx:for="{{postList}}" 
            wx:key="id" 
            class="post-list-item"
            bindtap="onMarkerTap"
            data-marker-id="{{item.id}}"
          >
            <!-- 帖子图片 -->
            <view class="post-item-image">
              <image 
                wx:if="{{item.images && item.images.length > 0}}" 
                class="post-image" 
                src="{{item.images[0]}}" 
                mode="aspectFill"
              ></image>
              <view wx:else class="post-image-placeholder">
                <text class="placeholder-text">{{item.category ? item.category.substring(0, 2) : 'PF'}}</text>
              </view>
            </view>

            <!-- 帖子内容 -->
            <view class="post-item-content">
              <view class="post-item-title">{{item.category || '附近帖子'}}</view>
              <view class="post-item-desc">{{item.content}}</view>
              <view class="post-item-meta">
                <view class="meta-item">
                  <image class="meta-icon" src="/assets/images/common/like.png" mode="aspectFit"></image>
                  <text class="meta-text">{{item.likeCount || 0}}</text>
                </view>
                <view class="meta-item">
                  <image class="meta-icon" src="/assets/images/common/comment.png" mode="aspectFit"></image>
                  <text class="meta-text">{{item.commentCount || 0}}</text>
                </view>
                <view class="meta-item" wx:if="{{item.distance}}">
                  <image class="meta-icon" src="/assets/images/common/location.png" mode="aspectFit"></image>
                  <text class="meta-text">{{item.distance}}</text>
                </view>
              </view>
            </view>

            <!-- 箭头指示 -->
            <view class="post-item-arrow">
              <image class="arrow-icon" src="/assets/images/common/arrow-right.png" mode="aspectFit"></image>
            </view>
          </view>
        </block>

        <!-- 空状态 -->
        <view wx:else class="post-list-empty">
          <text class="empty-text">暂无附近帖子</text>
        </view>
      </scroll-view>
    </view>
  </view>
</view>
