<!--home.wxml-->

<layout 
  id="layout" 
  showLocation="{{true}}"
  bind:locationChange="onLocationChange"
>
  <view class="container" style="--status-bar-height: {{statusBarHeight}}px">
    <view class="post-list">
      <post-item 
        wx:for="{{posts}}" 
        wx:key="id"
        post="{{item}}"
        showLikeAction="{{false}}"
        showFavoriteAction="{{false}}"
        showCompletedAction="{{false}}"
        userId="{{userId}}"
        bind:like="onLike"
        bind:favorite="onFavorite"
        bind:completed="onToggleCompleted"
      />
    </view>
  </view>
</layout> 