const { getPostDetail } = require('../../stores/postStore.js');
const { submitFeedback, recordShare, submitReport } = require('../../stores/feedbackStore.js');
const PublishStore = require('../../stores/publishStore.js');
const commentService = require('../../services/commentService.js');

Page({
  data: {
    post: null,
    loading: true,
    navBarHeight: 88,
    menuButtonHeight: 0,
    showTabbar: false,
    showMoreModal: false,
    isFavorited: false,
    // 反馈弹窗相关
    feedbackTags: [],
    showFeedbackDialog: false,
    feedbackDialogLoading: false,
    feedbackList: [],
    feedbackTotal: 0,
    feedbackPage: 1,
    feedbackPageSize: 10,
    // 举报弹窗相关
    showReportDialog: false,
    reportReason: '',
    reportDialogLoading: false,
    // 回复相关
    showReplyInput: false,
    replyContent: '',
    replyTarget: null,
    replyParentId: null,
    replyPlaceholder: '写下你的回复...',
    replySubmitDisabled: true,
    replySubmitting: false
  },

  onLoad(options) {
    const { id } = options;
    if (id) {
      this.loadPostDetail(id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }

    // 获取custom-nav组件实例
    this.customNav = this.selectComponent('#custom-nav');
  },

  // 导航栏准备完成事件
  onNavReady(e) {
    const navHeight = e.detail.height;
    const menuButtonHeight = e.detail.menuButtonHeight;
    this.setData({
      navBarHeight: navHeight,
      menuButtonHeight: menuButtonHeight
    });
  },

  onScroll(e) {
    const scrollTop = e.detail.scrollTop;
    // 控制tabbar显隐
    if (scrollTop > 100) {
      this.setData({ showTabbar: false });
    } else {
      this.setData({ showTabbar: true });
    }

    // 同步传递给custom-nav组件
    if (this.customNav && this.customNav.handleScroll) {
      this.customNav.handleScroll(scrollTop);
    }
  },

  // 加载帖子详情
  async loadPostDetail(id) {
    this.setData({ loading: true });
    try {
      const rawPost = await getPostDetail(id);
      if (rawPost) {
        // 兼容字段
        const user = rawPost.user || {};
        const category = rawPost.category || {};
        let images = [];
        if (Array.isArray(rawPost.images)) {
          images = rawPost.images;
        } else if (typeof rawPost.images === 'string' && rawPost.images) {
          images = rawPost.images.split(',').map(img => img.trim()).filter(Boolean);
        }
        let tags = Array.isArray(rawPost.tags) ? rawPost.tags : (typeof rawPost.tags === 'string' ? rawPost.tags.split(',').map(t => t.trim()).filter(Boolean) : []);
        let time = rawPost.createTime || '';
        let stats = rawPost.stats || {};
        // 兼容userId
        let userId = user.id || rawPost.userId || '';
        const post = {
          ...rawPost,
          avatar: user.avatar || '/assets/images/avatar.png',
          nickname: user.nickname || '匿名',
          images,
          tags,
          tag: category.name || '',
          description: rawPost.content || '',
          time,
          views: stats.viewCount || 0,
          comments: stats.feedbackCount || 0,
          likes: stats.likeCount || 0,
          isHighlight: rawPost.top === '1' || rawPost.top === 1,
          userId
        };
        // 匿名处理
        const isAnonymous = rawPost.isAnonymity === '1' || rawPost.isAnonymity === 1;
        this.setData({ post, isAnonymous });
        this.loadFeedbackTags(post.categoryId);
        await this.loadFeedbackList();
      } else {
        wx.showToast({
          title: '帖子不存在',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载帖子详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 点击更多图标
  onMoreTap() {
    console.log('onMoreTap');
    this.setData({
      showMoreModal: true
    });
  },

  // 隐藏更多弹窗
  hideMoreModal() {
    this.setData({
      showMoreModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },

  // 点击举报
  onReportTap() {
    const { post } = this.data;
    if (!post) return;

    wx.showActionSheet({
      itemList: ['虚假信息', '违法违规', '垃圾广告', '其他'],
      success: (res) => {
        const reasons = ['虚假信息', '违法违规', '垃圾广告', '其他'];
        const reason = reasons[res.tapIndex];
        
        this.setData({
          reportReason: reason,
          showReportDialog: true
        });
        this.hideMoreModal();
      }
    });
  },

  // 点击咨询
  onContactTap() {
    const { post } = this.data;
    if (!post) return;
    let contactType = post.contactType;
    let contactValue = '';
    if (contactType === 'phone') {
      contactValue = post.contactPhone;
    } else if (contactType === 'wechat') {
      contactValue = post.contactNumber;
    } else {
      // 默认优先手机号
      contactValue = post.contactPhone || post.contactNumber;
      contactType = post.contactPhone ? 'phone' : 'wechat';
    }
    if (!contactValue) {
      wx.showToast({ title: '暂无联系方式', icon: 'none' });
      return;
    }
    if (contactType === 'phone') {
      wx.showModal({
        title: '拨打电话',
        content: `是否拨打电话：${contactValue}`,
        success: (res) => {
          if (res.confirm) {
            wx.makePhoneCall({ phoneNumber: contactValue });
          }
        }
      });
    } else if (contactType === 'wechat') {
      wx.showModal({
        title: '复制微信号',
        content: `微信号：${contactValue}`,
        confirmText: '复制',
        success: (res) => {
          if (res.confirm) {
            wx.setClipboardData({ data: contactValue });
          }
        }
      });
    }
  },

  // 点击图片
  onImageTap(e) {
    const { index } = e.detail; // 从组件事件中获取index
    const { post } = this.data;
    
    if (post && post.images && post.images.length > 0) {
      wx.previewImage({
        current: post.images[index],
        urls: post.images
      });
    }
  },

  // 点赞
  onLikeTap() {
    const { post } = this.data;
    if (!post) return;

    // 这里可以调用点赞接口
    wx.showToast({
      title: '点赞成功',
      icon: 'success'
    });
  },

  // 收藏
  onFavoriteTap() {
    const { post, isFavorited } = this.data;
    if (!post) return;

    // 切换收藏状态
    this.setData({
      isFavorited: !isFavorited
    });

    // 这里可以调用收藏接口
    wx.showToast({
      title: isFavorited ? '取消收藏成功' : '收藏成功',
      icon: 'success'
    });

    // 如果是从弹窗点击的，关闭弹窗
    if (this.data.showMoreModal) {
      this.hideMoreModal();
    }
  },

  async loadFeedbackTags(categoryId) {
    const res = await PublishStore.getTagsByCategory(categoryId);
    if (res.success) {
      this.setData({ feedbackTags: res.data });
    } else {
      this.setData({ feedbackTags: [] });
    }
  },

  async onFeedbackTap() {
    const { post } = this.data;
    if (!post) return;
    this.setData({ feedbackDialogLoading: true });
    await this.loadFeedbackTags(post.categoryId);
    this.setData({
      showFeedbackDialog: true,
      feedbackDialogLoading: false
    });
  },

  onSelectFeedbackTag(e) {
    this.setData({ selectedFeedbackTag: e.currentTarget.dataset.tag });
  },
  onFeedbackContentInput(e) {
    this.setData({ feedbackContent: e.detail.value });
  },
  onFeedbackDialogCancel() {
    this.setData({ showFeedbackDialog: false });
  },
  async loadFeedbackList() {
    const { post, feedbackPage, feedbackPageSize, feedbackList: currentList } = this.data;
    if (!post) return;
    const res = await commentService.getFeedbackPage({ postId: post.id, current: feedbackPage, size: feedbackPageSize });
    if (res && res.data && res.data.records) {
      // 保存当前展开状态
      const expandedStates = {};
      if (currentList) {
        currentList.forEach(item => {
          if (item.expanded) {
            expandedStates[item.id.toString()] = {
              expanded: true,
              replies: item.replies || []
            };
          }
        });
      }

      // 处理反馈数据，格式化@用户内容
      const feedbackList = res.data.records.map(feedback => {
        const feedbackId = feedback.id.toString();
        const savedState = expandedStates[feedbackId];

        return {
          ...feedback,
          formattedContent: this.formatContentWithMentions(feedback.content),
          expanded: savedState ? savedState.expanded : false,
          replies: savedState ? savedState.replies : [],
          hasMoreReplies: feedback.replyCount > (savedState && savedState.replies ? savedState.replies.length : 0)
        };
      });

      this.setData({
        feedbackList: feedbackList,
        feedbackTotal: res.data.total
      });
    }
  },

  // 格式化@用户内容
  formatContentWithMentions(content) {
    if (!content) return '';

    // 处理@用户高亮
    return content.replace(
      /@([^\s@]+)/g,
      '<span style="color: #007aff; font-weight: 500;">@$1</span>'
    );
  },

  // 格式化时间显示
  formatTime(timeStr) {
    if (!timeStr) return '';

    const time = new Date(timeStr);
    const now = new Date();
    const diff = now - time;

    // 小于1分钟
    if (diff < 60000) {
      return '刚刚';
    }

    // 小于1小时
    if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    }

    // 小于24小时
    if (diff < 86400000) {
      return Math.floor(diff / 3600000) + '小时前';
    }

    // 小于7天
    if (diff < 604800000) {
      return Math.floor(diff / 86400000) + '天前';
    }

    // 超过7天显示具体日期
    return time.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  },
  async onFeedbackDialogSubmit(e) {
    const { tag, content } = e.detail;
    const { post } = this.data;
    this.setData({ feedbackDialogLoading: true });
    wx.showLoading({ title: '提交中...' });
    try {
      await submitFeedback({ postId: post.id, content, reason: tag });
      wx.showToast({ title: '反馈已提交', icon: 'success' });
      this.setData({ showFeedbackDialog: false });
      await this.loadFeedbackList(); // 提交后刷新反馈列表
    } catch (e) {
      wx.showToast({ title: '提交失败', icon: 'none' });
    } finally {
      wx.hideLoading();
      this.setData({ feedbackDialogLoading: false });
    }
  },

  async onFeedbackLikeTap(e) {
    const { feedbackId, isHelpful } = e.detail;
    if (!feedbackId) return;

    // 本地乐观更新
    const { feedbackList } = this.data;
    const feedbackIndex = feedbackList.findIndex(item => item.id === feedbackId);
    if (feedbackIndex === -1) return;

    const feedback = feedbackList[feedbackIndex];
    const newIsHelpful = !isHelpful;
    const newHelpfulCount = newIsHelpful ? (feedback.helpfulCount + 1) : (feedback.helpfulCount - 1);

    // 立即更新本地数据
    const newFeedbackList = [...feedbackList];
    newFeedbackList[feedbackIndex] = {
      ...feedback,
      isHelpful: newIsHelpful,
      helpfulCount: newHelpfulCount
    };

    this.setData({
      feedbackList: newFeedbackList
    });
    
    wx.showLoading({ title: '正在标记...' });
    try {
      await commentService.toggleHelpful(feedbackId);
      wx.showToast({
        title: newIsHelpful ? '已标记有帮助' : '已取消标记',
        icon: 'success'
      });
    } catch (err) {
      // 接口失败，回滚本地数据
      const rollbackList = [...feedbackList];
      rollbackList[feedbackIndex] = {
        ...feedback,
        isHelpful: isHelpful,
        helpfulCount: feedback.helpfulCount
      };
      this.setData({
        feedbackList: rollbackList
      });
      wx.showToast({ title: '操作失败', icon: 'none' });
    } finally {
      wx.hideLoading();
    }
  },

  // 分享
  onShareTap() {
    console.log('分享按钮被点击');
    const { post } = this.data;
    if (!post) {
      wx.showToast({ title: '帖子信息不存在', icon: 'none' });
      return;
    }

    console.log('当前帖子信息:', post);

    wx.showActionSheet({
      itemList: ['分享给朋友', '分享到朋友圈', '复制链接'],
      success: async (res) => {
        console.log('用户选择了分享选项:', res.tapIndex);
        try {
        if (res.tapIndex === 0) {
            // 分享给朋友 - 直接触发分享
            console.log('记录分享给朋友');
          wx.showToast({
              title: '请点击右上角分享', 
              icon: 'none',
              duration: 2000
          });
            // 记录分享行为
            const shareResult = await recordShare({ postId: post.id, type: 'wechat' });
            console.log('分享记录结果:', shareResult);
        } else if (res.tapIndex === 1) {
            // 分享到朋友圈
            console.log('记录分享到朋友圈');
            wx.showToast({ 
              title: '请点击右上角分享到朋友圈', 
              icon: 'none',
              duration: 2000
            });
            const shareResult = await recordShare({ postId: post.id, type: 'moments' });
            console.log('分享记录结果:', shareResult);
          } else if (res.tapIndex === 2) {
          // 复制链接
            console.log('复制链接');
            const shareText = `【${post.title}】\n${post.description || '查看详情'}\n\n点击查看：${post.id}`;
          wx.setClipboardData({
              data: shareText,
            success: () => {
                wx.showToast({ title: '链接已复制', icon: 'success' });
                recordShare({ postId: post.id, type: 'link' }).then(result => {
                  console.log('链接分享记录结果:', result);
              });
            }
          });
        }
        } catch (error) {
          console.error('分享操作失败:', error);
          wx.showToast({ title: '分享失败', icon: 'none' });
        }
      },
      fail: (error) => {
        console.error('分享选择失败:', error);
        wx.showToast({ title: '分享选择失败', icon: 'none' });
      }
    });
  },

  // 返回
  onBackTap() {
    wx.navigateBack();
  },

  // 分享配置
  onShareAppMessage() {
    const { post } = this.data;
    if (!post) {
      return {
        title: '发现好内容',
        path: '/pages/index/index'
      };
    }

    // 右上角菜单分享：这里直接记录（无法判断是否真的转发成功）
    recordShare({ postId: post.id, type: 'wechat' });

    return {
      title: post.title,
      path: `/pages/post-detail/post-detail?id=${post.id}`,
      imageUrl: post.images && post.images.length > 0 ? post.images[0] : '',
      // 页面按钮分享：只有实际分享成功才会回调
      success: () => {
        recordShare({ postId: post.id, type: 'wechat' });
      }
    };
  },

  // 举报弹窗相关方法
  onReportDialogCancel() {
    this.setData({ 
      showReportDialog: false,
      reportReason: '',
      reportDialogLoading: false
    });
  },

  async onReportDialogSubmit(e) {
    const { content, images } = e.detail;
    const { post, reportReason } = this.data;
      // 提交举报
      await submitReport({
        postId: post.id,
        reason: reportReason,
        content: content,
        images: images.join(',')
      });

      wx.showToast({ title: '举报已提交', icon: 'success' });
      this.onReportDialogCancel();
  },

  onShareTimeline() {
    const { post } = this.data;
    if (!post) {
      return {
        title: '发现好内容'
      };
    }

    return {
      title: post.title,
      imageUrl: post.images && post.images.length > 0 ? post.images[0] : ''
    };
  },

  onUserCardTap(e) {
    const userId = e.currentTarget.dataset.userid;
    wx.navigateTo({
      url: `/pkg_user/pages/user-detail/user-detail?userId=${userId}`
    });
  },

  // 评论回复相关事件处理器

  // 处理评论回复事件
  onCommentReply(e) {
    const { commentId, parentId, replyToComment } = e.detail;

    // 显示回复输入框
    this.setData({
      showReplyInput: true,
      replyTarget: replyToComment,
      replyParentId: (parentId || commentId).toString(),
      replyContent: `@${replyToComment.nickname} `,
      replyPlaceholder: `回复 @${replyToComment.nickname}`,
      replySubmitDisabled: true // 初始状态禁用发送按钮
    });
  },

  // 回复输入处理
  onReplyInput(e) {
    const value = e.detail.value;
    const { replyTarget } = this.data;

    let finalContent = value;

    // 确保@用户名始终在开头
    if (!value.startsWith(`@${replyTarget.nickname} `)) {
      finalContent = `@${replyTarget.nickname} ${value.replace(`@${replyTarget.nickname}`, '').trim()}`;
    }

    // 检查是否有实际内容（除了@用户名）
    const contentWithoutMention = finalContent.replace(`@${replyTarget.nickname}`, '').trim();
    const isDisabled = !contentWithoutMention;

    this.setData({
      replyContent: finalContent,
      replySubmitDisabled: isDisabled
    });
  },

  // 取消回复
  onCancelReply() {
    this.setData({
      showReplyInput: false,
      replyContent: '',
      replyTarget: null,
      replyParentId: null,
      replySubmitDisabled: true,
      replySubmitting: false
    });
  },

  // 提交回复
  async onSubmitReply() {
    const { replyContent, replyTarget, replyParentId, post } = this.data;

    // 检查是否有实际内容（除了@用户名）
    const contentWithoutMention = replyContent.replace(`@${replyTarget.nickname}`, '').trim();
    if (!contentWithoutMention) {
      wx.showToast({
        title: '请输入回复内容',
        icon: 'none'
      });
      return;
    }

    // 设置提交状态
    this.setData({ replySubmitting: true });
    wx.showLoading({ title: '发送中...' });

    try {
      const replyData = {
        postId: post.id.toString(),
        parentId: replyParentId.toString(),
        content: replyContent.trim(),
        replyToUserId: replyTarget.userId.toString(),
        replyToUserName: replyTarget.nickname
      };

      const result = await commentService.replyComment(replyData);

      if (result && (result.code === 200 || result.success)) {
        wx.showToast({
          title: '回复成功',
          icon: 'success'
        });

        // 清空回复框
        this.onCancelReply();

        // 刷新评论列表
        await this.loadFeedbackList();
      } else {
        throw new Error(result.msg || '回复失败');
      }
    } catch (error) {
      console.error('回复失败:', error);
      wx.showToast({
        title: error.message || '回复失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
      this.setData({ replySubmitting: false });
    }
  },

  // 处理展开/收起回复事件
  onToggleReplies(e) {
    const { commentId, expanded } = e.detail;
    const { feedbackList } = this.data;

    // 更新评论的展开状态
    const updatedList = feedbackList.map(comment => {
      if (comment.id.toString() === commentId.toString()) {
        return {
          ...comment,
          expanded: expanded
        };
      }
      return comment;
    });

    this.setData({ feedbackList: updatedList });
  },

  // 处理加载回复列表事件
  async onLoadReplies(e) {
    const { commentId } = e.detail;

    // 显示加载状态
    wx.showLoading({ title: '加载回复中...' });

    try {
      // 调用API加载回复列表
      const result = await this.loadCommentReplies(commentId);
      if (result.code === 200 && result.data) {
        // 更新评论的回复列表
        this.updateCommentReplies(commentId, result.data);
      } else {
        console.error('加载回复失败:', result.msg);
        wx.showToast({
          title: result.msg || '加载回复失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载回复失败:', error);
      wx.showToast({
        title: '加载回复失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 处理回复点赞事件
  async onReplyLike(e) {
    const { replyId, isLiked } = e.detail;
    try {
      // 调用API点赞回复
      const result = await this.likeReply(replyId, !isLiked);
      if (result.success) {
        // 更新回复的点赞状态
        this.updateReplyLikeStatus(replyId, !isLiked);
      }
    } catch (error) {
      console.error('点赞回复失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 处理加载更多回复事件
  async onLoadMoreReplies(e) {
    const { commentId } = e.detail;
    try {
      // 调用API加载更多回复
      const result = await this.loadMoreCommentReplies(commentId);
      if (result.success) {
        // 追加回复列表
        this.appendCommentReplies(commentId, result.data);
      }
    } catch (error) {
      console.error('加载更多回复失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // API调用方法

  // 加载评论回复列表
  async loadCommentReplies(commentId) {
    return await commentService.getCommentReplies(commentId);
  },

  // 点赞回复
  async likeReply(replyId, isLike) {
    return await commentService.likeComment(replyId, isLike ? 'LIKE' : 'UNLIKE');
  },

  // 加载更多回复
  async loadMoreCommentReplies(commentId, page = 1) {
    return await commentService.getCommentReplies(commentId, { page, size: 10 });
  },

  // 数据更新方法

  // 更新评论的回复列表
  updateCommentReplies(commentId, replies) {
    const { feedbackList } = this.data;
    const updatedList = feedbackList.map(comment => {
      if (comment.id.toString() === commentId.toString()) {
        // 格式化回复内容
        const formattedReplies = replies.map(reply => ({
          ...reply,
          formattedContent: this.formatContentWithMentions(reply.content),
          avatar: reply.avatar || '/assets/images/common/default-avatar.png', // 默认头像
          createTime: this.formatTime(reply.createTime) // 格式化时间
        }));

        return {
          ...comment,
          replies: formattedReplies,
          expanded: true
        };
      }
      return comment;
    });

    this.setData({ feedbackList: updatedList });
  },

  // 更新回复点赞状态
  updateReplyLikeStatus(replyId, isLiked) {
    const { feedbackList } = this.data;
    const updatedList = feedbackList.map(comment => {
      if (comment.replies && comment.replies.length > 0) {
        const updatedReplies = comment.replies.map(reply => {
          if (reply.id === replyId) {
            return {
              ...reply,
              isLiked: isLiked,
              likeCount: isLiked ? reply.likeCount + 1 : reply.likeCount - 1
            };
          }
          return reply;
        });

        return {
          ...comment,
          replies: updatedReplies
        };
      }
      return comment;
    });

    this.setData({ feedbackList: updatedList });
  },

  // 追加评论回复
  appendCommentReplies(commentId, newReplies) {
    const { feedbackList } = this.data;
    const updatedList = feedbackList.map(comment => {
      if (comment.id === commentId) {
        // 合并并去重
        const allReplies = [...(comment.replies || []), ...newReplies];
        const uniqueReplies = [];
        const idSet = new Set();
        for (const reply of allReplies) {
          if (!idSet.has(reply.id)) {
            uniqueReplies.push(reply);
            idSet.add(reply.id);
          }
        }
        return {
          ...comment,
          replies: uniqueReplies
        };
      }
      return comment;
    });

    this.setData({ feedbackList: updatedList });
  }
});