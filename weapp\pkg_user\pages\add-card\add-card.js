// pkg_user/pages/add-card/add-card.js
const { request } = require('../../../utils/request');

Page({
  data: {
    // 表单数据
    formData: {
      name: '',
      company: '',
      position: '',
      phone: '',
      email: '',
      address: '',
      website: '',
      wechat: '',
      category: 'business',
      remark: '',
      avatar: ''
    },
    // 分类选项
    categories: [
      { id: 'business', name: '商务' },
      { id: 'personal', name: '个人' },
      { id: 'friend', name: '朋友' },
      { id: 'colleague', name: '同事' },
      { id: 'client', name: '客户' }
    ],
    // 提交状态
    submitting: false
  },

  onLoad(options) {
    console.log('添加名片页面加载');
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 分类选择
  onCategoryChange(e) {
    const categoryId = e.currentTarget.dataset.id;
    this.setData({
      'formData.category': categoryId
    });
  },

  // 选择头像
  onChooseAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.setData({
          'formData.avatar': tempFilePath
        });
      }
    });
  },

  // 扫描名片
  onScanCard() {
    wx.scanCode({
      success: (res) => {
        console.log('扫描结果:', res);
        // 这里可以解析二维码中的名片信息
        wx.showToast({
          title: '扫描功能开发中',
          icon: 'none'
        });
      },
      fail: () => {
        wx.showToast({
          title: '扫描失败',
          icon: 'none'
        });
      }
    });
  },

  // 提交名片
  async onSubmit() {
    if (this.data.submitting) return;

    // 表单验证
    if (!this.validateForm()) {
      return;
    }

    try {
      this.setData({ submitting: true });

      // 模拟API调用
      await this.mockSubmitCard();

      wx.showToast({
        title: '添加成功',
        icon: 'success'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      console.error('添加名片失败:', error);
      wx.showToast({
        title: '添加失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 模拟提交名片
  mockSubmitCard() {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('提交名片数据:', this.data.formData);
        resolve();
      }, 1000);
    });
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;
    
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return false;
    }

    if (!formData.phone.trim()) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return false;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return false;
    }

    // 验证邮箱格式（如果填写了）
    if (formData.email && formData.email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        wx.showToast({
          title: '请输入正确的邮箱',
          icon: 'none'
        });
        return false;
      }
    }

    return true;
  },

  // 预览名片
  onPreview() {
    if (!this.validateForm()) {
      return;
    }

    // 跳转到名片详情页面预览
    const cardData = encodeURIComponent(JSON.stringify(this.data.formData));
    wx.navigateTo({
      url: `/pkg_user/pages/card-detail/card-detail?data=${cardData}&preview=true`
    });
  }
});
