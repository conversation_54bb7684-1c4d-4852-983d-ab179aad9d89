/**
 * 帖子详情页 - 重构版本
 * 采用模块化架构，分离关注点，提高代码可维护性
 */

// 导入模块
const { PostDataManager, FeedbackDataManager, CommentDataManager } = require('./services/dataManager');
const { MESSAGES, PAGINATION, LIMITS } = require('./config/constants');
const { contentUtils, debounce, throttle, storageUtils } = require('./utils/helpers');

const app = getApp();

Page({
  data: {
    // 基础数据
    postId: '',
    postDetail: {},
    feedbackList: [],
    
    // UI状态
    loading: true,
    isLoadingMore: false,
    hasMore: true,
    navBarHeight: 88,
    menuButtonHeight: 0,
    showTabbar: false,
    
    // 模态框状态
    showMoreModal: false,
    showFeedbackDialog: false,
    showReportDialog: false,
    
    // 反馈相关
    feedbackTags: [],
    feedbackDialogLoading: false,
    feedbackContent: '',
    selectedTags: [],
    canSubmitFeedback: false,
    isSubmittingFeedback: false,
    
    // 回复相关
    showReplyInput: false,
    replyContent: '',
    replyTarget: null,
    replyParentId: null,
    replyPlaceholder: '写下你的回复...',
    canSubmitReply: false,
    isSubmittingReply: false,
    
    // 举报相关
    reportReason: '',
    reportDialogLoading: false
  },

  // 数据管理器实例
  postManager: null,
  feedbackManager: null,
  commentManager: null,

  /**
   * 页面加载
   */
  onLoad(options) {
    this.initializeManagers();
    this.initializePage(options);
  },

  /**
   * 初始化数据管理器
   */
  initializeManagers() {
    this.postManager = new PostDataManager();
    this.feedbackManager = new FeedbackDataManager();
    this.commentManager = new CommentDataManager();
  },

  /**
   * 初始化页面
   */
  async initializePage(options) {
    const { id } = options;
    
    if (!id) {
      this.showErrorAndGoBack('参数错误');
      return;
    }

    this.setData({ postId: id });
    
    try {
      // 并行加载数据
      await Promise.all([
        this.loadPostDetail(id),
        this.loadFeedbackList(id, true)
      ]);
    } catch (error) {
      console.error('页面初始化失败:', error);
      this.showErrorAndGoBack('加载失败');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 显示错误并返回
   */
  showErrorAndGoBack(message) {
    wx.showToast({
      title: message,
      icon: 'none'
    });
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  },

  /**
   * 加载帖子详情
   */
  async loadPostDetail(postId) {
    try {
      const postDetail = await this.postManager.loadPostDetail(postId);
      this.setData({ postDetail });
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: postDetail.title || '帖子详情'
      });
      
    } catch (error) {
      console.error('加载帖子详情失败:', error);
      wx.showToast({
        title: MESSAGES.ERROR.LOAD_POST_FAILED,
        icon: 'none'
      });
      throw error;
    }
  },

  /**
   * 加载反馈列表
   */
  async loadFeedbackList(postId, refresh = false) {
    if (this.data.isLoadingMore && !refresh) return;

    this.setData({ 
      isLoadingMore: !refresh,
      loading: refresh 
    });

    try {
      const feedbackList = await this.feedbackManager.loadFeedbackList(postId, refresh);
      
      this.setData({
        feedbackList,
        hasMore: this.feedbackManager.pagination.hasMore
      });
      
    } catch (error) {
      console.error('加载反馈列表失败:', error);
      wx.showToast({
        title: MESSAGES.ERROR.LOAD_FEEDBACK_FAILED,
        icon: 'none'
      });
    } finally {
      this.setData({ 
        isLoadingMore: false,
        loading: false 
      });
    }
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      await this.loadFeedbackList(this.data.postId, true);
    } finally {
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: throttle(function() {
    if (this.data.hasMore && !this.data.isLoadingMore) {
      this.loadFeedbackList(this.data.postId, false);
    }
  }, 500),

  /**
   * 点赞帖子
   */
  async onLikePost() {
    const { postDetail } = this.data;
    const newLikeState = !postDetail.userActions.isLiked;
    
    try {
      const success = await this.postManager.toggleLike(newLikeState);
      
      if (success) {
        this.setData({ 
          'postDetail.userActions.isLiked': newLikeState,
          'postDetail.stats.likeCount': this.postManager.postData.stats.likeCount
        });
        
        wx.showToast({
          title: newLikeState ? MESSAGES.SUCCESS.LIKE_POST : MESSAGES.SUCCESS.UNLIKE_POST,
          icon: 'success'
        });
      }
    } catch (error) {
      wx.showToast({
        title: MESSAGES.ERROR.LIKE_FAILED,
        icon: 'none'
      });
    }
  },

  /**
   * 收藏帖子
   */
  async onFavoritePost() {
    const { postDetail } = this.data;
    const newFavoriteState = !postDetail.userActions.isFavorited;
    
    try {
      const success = await this.postManager.toggleFavorite(newFavoriteState);
      
      if (success) {
        this.setData({ 
          'postDetail.userActions.isFavorited': newFavoriteState,
          'postDetail.stats.favoriteCount': this.postManager.postData.stats.favoriteCount
        });
        
        wx.showToast({
          title: newFavoriteState ? MESSAGES.SUCCESS.FAVORITE_POST : MESSAGES.SUCCESS.UNFAVORITE_POST,
          icon: 'success'
        });
      }
    } catch (error) {
      wx.showToast({
        title: MESSAGES.ERROR.FAVORITE_FAILED,
        icon: 'none'
      });
    }
  },

  /**
   * 反馈内容输入
   */
  onFeedbackInput: debounce(function(e) {
    const content = e.detail.value.trim();
    const canSubmit = contentUtils.validateContentLength(content, LIMITS.FEEDBACK_CONTENT_MAX);
    
    this.setData({
      feedbackContent: content,
      canSubmitFeedback: canSubmit && this.data.selectedTags.length > 0
    });
  }, 300),

  /**
   * 回复内容输入
   */
  onReplyInput: debounce(function(e) {
    const content = e.detail.value.trim();
    const canSubmit = contentUtils.validateContentLength(content, LIMITS.REPLY_CONTENT_MAX);
    
    this.setData({
      replyContent: content,
      canSubmitReply: canSubmit
    });
  }, 300),

  /**
   * 展开/收起回复
   */
  onToggleReplies(e) {
    const { commentId, expanded } = e.detail;
    
    // 更新展开状态
    this.feedbackManager.setExpandedState(commentId, expanded);
    
    // 更新UI
    const { feedbackList } = this.data;
    const updatedList = feedbackList.map(comment => {
      if (comment.id === commentId) {
        return { ...comment, expanded };
      }
      return comment;
    });
    
    this.setData({ feedbackList: updatedList });
  },

  /**
   * 加载回复列表
   */
  async onLoadReplies(e) {
    const { commentId } = e.detail;

    try {
      const replies = await this.commentManager.loadReplies(commentId);
      this.feedbackManager.updateCommentReplies(commentId, replies);

      // 更新UI
      const { feedbackList } = this.data;
      const updatedList = feedbackList.map(comment => {
        if (comment.id === commentId) {
          return { ...comment, replies };
        }
        return comment;
      });

      this.setData({ feedbackList: updatedList });

    } catch (error) {
      wx.showToast({
        title: MESSAGES.ERROR.LOAD_REPLIES_FAILED,
        icon: 'none'
      });
    }
  },

  /**
   * 提交反馈
   */
  async onSubmitFeedback() {
    const { feedbackContent, selectedTags, postId } = this.data;

    if (!contentUtils.validateContentLength(feedbackContent, LIMITS.FEEDBACK_CONTENT_MAX)) {
      wx.showToast({
        title: MESSAGES.VALIDATION.FEEDBACK_CONTENT_REQUIRED,
        icon: 'none'
      });
      return;
    }

    if (selectedTags.length === 0) {
      wx.showToast({
        title: MESSAGES.VALIDATION.FEEDBACK_TAG_REQUIRED,
        icon: 'none'
      });
      return;
    }

    this.setData({ isSubmittingFeedback: true });

    try {
      const feedbackData = {
        postId,
        content: feedbackContent,
        tags: selectedTags
      };

      const success = await this.feedbackManager.submitFeedback(feedbackData);

      if (success) {
        wx.showToast({
          title: MESSAGES.SUCCESS.SUBMIT_FEEDBACK,
          icon: 'success'
        });

        // 重置表单并关闭弹窗
        this.setData({
          feedbackContent: '',
          selectedTags: [],
          showFeedbackDialog: false,
          canSubmitFeedback: false
        });

        // 刷新列表
        this.setData({ feedbackList: this.feedbackManager.feedbackList });
      }
    } catch (error) {
      wx.showToast({
        title: MESSAGES.ERROR.SUBMIT_FEEDBACK_FAILED,
        icon: 'none'
      });
    } finally {
      this.setData({ isSubmittingFeedback: false });
    }
  },

  /**
   * 提交回复
   */
  async onSubmitReply() {
    const { replyContent, replyTarget, replyParentId, postId } = this.data;

    if (!contentUtils.validateContentLength(replyContent, LIMITS.REPLY_CONTENT_MAX)) {
      wx.showToast({
        title: MESSAGES.VALIDATION.REPLY_CONTENT_REQUIRED,
        icon: 'none'
      });
      return;
    }

    this.setData({ isSubmittingReply: true });

    try {
      const replyData = {
        postId,
        parentId: replyParentId,
        content: replyContent,
        mentionedUserIds: replyTarget ? [replyTarget.userId] : []
      };

      const newReply = await this.commentManager.submitReply(replyData);

      wx.showToast({
        title: MESSAGES.SUCCESS.SUBMIT_REPLY,
        icon: 'success'
      });

      // 重置回复表单
      this.setData({
        showReplyInput: false,
        replyContent: '',
        replyTarget: null,
        replyParentId: null,
        canSubmitReply: false
      });

      // 刷新反馈列表
      await this.loadFeedbackList(postId, true);

    } catch (error) {
      wx.showToast({
        title: MESSAGES.ERROR.SUBMIT_REPLY_FAILED,
        icon: 'none'
      });
    } finally {
      this.setData({ isSubmittingReply: false });
    }
  },

  /**
   * 点赞反馈
   */
  async onLikeFeedback(e) {
    const { feedbackId } = e.detail;

    try {
      const success = await this.feedbackManager.toggleFeedbackLike(feedbackId);

      if (success) {
        // 更新UI
        this.setData({ feedbackList: this.feedbackManager.feedbackList });

        wx.showToast({
          title: MESSAGES.SUCCESS.LIKE_POST,
          icon: 'success'
        });
      }
    } catch (error) {
      wx.showToast({
        title: MESSAGES.ERROR.LIKE_FAILED,
        icon: 'none'
      });
    }
  },

  /**
   * 显示回复输入框
   */
  onShowReplyInput(e) {
    const { comment } = e.detail;

    this.setData({
      showReplyInput: true,
      replyTarget: comment,
      replyParentId: comment.id,
      replyPlaceholder: `回复 @${comment.author.nickname}`,
      replyContent: '',
      canSubmitReply: false
    });
  },

  /**
   * 隐藏回复输入框
   */
  onHideReplyInput() {
    this.setData({
      showReplyInput: false,
      replyContent: '',
      replyTarget: null,
      replyParentId: null,
      canSubmitReply: false
    });
  },

  /**
   * 显示更多操作
   */
  onShowMoreModal() {
    this.setData({ showMoreModal: true });
  },

  /**
   * 隐藏更多操作
   */
  onHideMoreModal() {
    this.setData({ showMoreModal: false });
  },

  /**
   * 显示反馈弹窗
   */
  onShowFeedbackDialog() {
    this.setData({
      showFeedbackDialog: true,
      showMoreModal: false
    });
  },

  /**
   * 隐藏反馈弹窗
   */
  onHideFeedbackDialog() {
    this.setData({ showFeedbackDialog: false });
  },

  /**
   * 分享帖子
   */
  async onSharePost() {
    try {
      await this.postManager.sharePost();
      wx.showToast({
        title: MESSAGES.SUCCESS.SHARE_POST,
        icon: 'success'
      });
    } catch (error) {
      wx.showToast({
        title: MESSAGES.ERROR.SHARE_FAILED,
        icon: 'none'
      });
    }
  },

  /**
   * 复制链接
   */
  onCopyLink() {
    const { postId } = this.data;
    const link = `pages/post-detail/post-detail?id=${postId}`;

    wx.setClipboardData({
      data: link,
      success: () => {
        wx.showToast({
          title: MESSAGES.SUCCESS.COPY_LINK,
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: MESSAGES.ERROR.COPY_FAILED,
          icon: 'none'
        });
      }
    });
  },

  /**
   * 预览图片
   */
  onPreviewImage(e) {
    const { src } = e.detail;
    const { postDetail } = this.data;

    wx.previewImage({
      current: src,
      urls: postDetail.images || [src]
    });
  }
});
