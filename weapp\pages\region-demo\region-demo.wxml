<!--地区选择功能演示页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-title">地区选择功能演示</view>

  <!-- 当前选择的地区 -->
  <view class="current-location-section">
    <view class="section-title">当前选择的地区</view>
    <view class="location-display">
      <view class="location-info">
        <view class="location-full">{{selectedLocation || '未选择'}}</view>
        <view class="location-short">显示名称: {{selectedLocationName || '未选择'}}</view>
        <view wx:if="{{selectedRegion}}" class="location-code">
          地区编码: {{selectedRegion.regionCode}}
        </view>
      </view>
      <button class="select-btn" bindtap="showRegionSelector">
        {{selectedLocation ? '重新选择' : '选择地区'}}
      </button>
    </view>
  </view>

  <!-- 选择路径 -->
  <view wx:if="{{selectedRegions.length > 0}}" class="selection-path-section">
    <view class="section-title">选择路径</view>
    <view class="path-container">
      <view 
        wx:for="{{selectedRegions}}" 
        wx:key="regionCode"
        class="path-item">
        <text class="path-level">{{item.level}}级</text>
        <text class="path-name">{{item.regionName}}</text>
        <text class="path-code">({{item.regionCode}})</text>
      </view>
    </view>
  </view>

  <!-- 搜索功能 -->
  <view class="search-section">
    <view class="section-title">搜索地区</view>
    <view class="search-container">
      <input 
        class="search-input"
        placeholder="输入地区名称搜索"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        confirm-type="search"
        bindconfirm="searchRegions"
      />
      <button class="search-btn" bindtap="searchRegions">搜索</button>
    </view>
    
    <!-- 搜索结果 -->
    <view wx:if="{{searchResults.length > 0}}" class="search-results">
      <view class="results-title">搜索结果</view>
      <view 
        wx:for="{{searchResults}}" 
        wx:key="regionCode"
        class="result-item"
        data-region="{{item}}"
        bindtap="selectSearchResult">
        <view class="result-name">{{item.regionName}}</view>
        <view class="result-info">
          <text class="result-level">{{item.level}}级</text>
          <text class="result-code">{{item.regionCode}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能按钮 -->
  <view class="action-section">
    <view class="section-title">功能操作</view>
    <view class="action-buttons">
      <button class="action-btn" bindtap="viewFullAddress">
        查看完整地址信息
      </button>
      <button class="action-btn" bindtap="getCurrentLocation">
        获取当前位置
      </button>
      <button class="action-btn danger" bindtap="clearCache">
        清除缓存
      </button>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="help-section">
    <view class="section-title">使用说明</view>
    <view class="help-content">
      <view class="help-item">1. 点击"选择地区"按钮打开地区选择器</view>
      <view class="help-item">2. 先选择城市，再选择区县</view>
      <view class="help-item">3. 选择完成后会自动缓存到本地</view>
      <view class="help-item">4. 支持搜索功能快速查找地区</view>
      <view class="help-item">5. 缓存数据24小时后自动过期</view>
    </view>
  </view>
</view>

<!-- 地区选择器 -->
<region-picker
  show="{{showRegionPicker}}"
  max-level="{{3}}"
  default-value="{{selectedRegion ? selectedRegion.regionCode : ''}}"
  bind:confirm="onRegionConfirm"
  bind:close="onRegionClose"
  bind:showchange="onRegionShowChange">
</region-picker>
