.container {
  padding: 0;
  background: #fff;
  min-height: 100vh;
}

/* 搜索头部 */
.search-header {
  padding: 12rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-bar {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
  color: #333;
}

.placeholder {
  color: #999;
}

.clear-btn {
  padding: 8rpx;
}

.clear-icon {
  width: 28rpx;
  height: 28rpx;
}

.cancel-btn {
  font-size: 28rpx;
  color: #666;
  padding: 8rpx 0;
}

/* AI助手 */
.ai-helper {
  margin: 24rpx;
  padding: 24rpx;
  background: rgba(51, 127, 255, 0.1);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.ai-icon {
  width: 48rpx;
  height: 48rpx;
}

.ai-text {
  font-size: 28rpx;
  color: #337fff;
}

/* 分类标签 */
.category-section {
  padding: 0 24rpx;
  margin-bottom: 24rpx;
}

.category-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.category-item {
  padding: 12rpx 24rpx;
  background: #f5f5f5;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #333;
}

.category-item.active {
  background: #337fff;
  color: #fff;
}

/* 推荐帖子 */
.recommend-section {
  padding: 0 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 24rpx;
}

.post-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
