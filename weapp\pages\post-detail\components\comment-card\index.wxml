<view class="comment-card">
  <!-- 评论头部：头像、用户信息、点赞 -->
  <view class="comment-header">
    <image class="avatar" src="{{avatar}}" mode="aspectFill" />
    <view class="user-info">
      <view class="user-name-line">
        <text class="nickname">{{nickname}}</text>
        <text wx:if="{{replyToUserName}}" class="reply-to">回复 @{{replyToUserName}}</text>
      </view>
      <text class="time">{{time}}</text>
    </view>

    <!-- 右侧点赞区域 -->
    <view class="like-area" bindtap="onLikeTap">
      <image
        class="like-icon {{isHelpful ? 'helpful' : ''}}"
        src="{{isHelpful ? '/assets/images/detail/helpful-active.png' : '/assets/images/detail/helpful-none.png'}}"
      />
      <text class="like-count {{isHelpful ? 'helpful' : ''}}">{{likes || 0}}</text>
    </view>
  </view>

  <!-- 评论内容 -->
  <view class="comment-content">
    <view class="content-text reply-inline-row">
      <text class="comment-rich-text">{{formattedContent || content}}</text>
      <text class="inline-reply-btn" style="font-size:24rpx;height:32rpx;line-height:32rpx;padding:0 10rpx;" bindtap="onReplyTap">回复</text>
    </view>

    <!-- 评论图片 -->
    <view wx:if="{{image}}" class="comment-image">
      <image
        src="{{image}}"
        mode="aspectFill"
        bindtap="onPreviewImage"
        data-src="{{image}}"
      />
    </view>
  </view>

  <!-- 展开回复区域 -->
  <view wx:if="{{replyCount > 0 && !allRepliesLoaded}}" class="expand-replies-section" bindtap="onToggleReplies">
    <view class="expand-content compact">
      <text class="expand-text">{{expanded ? '收起' : '展开'}}{{replyCount}}条回复</text>
    </view>
  </view>

  <!-- 回复列表 -->
  <view wx:if="{{expanded}}" class="replies-container">
    <!-- 有回复时显示回复列表 -->
    <block wx:if="{{replies && replies.length > 0}}">
      <view
        wx:for="{{replies}}"
        wx:key="id"
        wx:for-item="reply"
        class="reply-item"
      >
      <!-- 回复内容 -->
      <view class="reply-content">
        <image class="reply-avatar" src="{{reply.avatar || .}}" mode="aspectFill" />
        <view class="reply-info">
          <view class="reply-header">
            <text class="reply-nickname">{{reply.nickname}}</text>
          </view>
          <!-- 回复内容和操作区域 -->
          <view class="reply-content-wrapper">
            <view class="reply-text-with-actions">
              <view class="reply-text-front">回复</view>
              <view wx:if="{{reply.replyToUserName}}" class="reply-text-front-name">
                {{reply.replyToUserName}}：
              </view>
              <rich-text class="reply-content-text" nodes="{{reply.formattedContent || reply.content}}"></rich-text>
            </view>
          </view>

          <!-- 回复图片 -->
          <view wx:if="{{reply.image}}" class="reply-image">
            <image
              src="{{reply.image}}"
              mode="aspectFill"
              bindtap="onPreviewReplyImage"
              data-src="{{reply.image}}"
            />
          </view>

          <!-- 回复底部 -->
          <view class="reply-footer">
            <view class="reply-footer-left">
              <text class="reply-time-footer">{{reply.createTime}}</text>
            </view>
            <view class="reply-footer-right">
              <text class="reply-inline-btn-footer" bindtap="onReplyToReply" data-reply="{{reply}}">回复</text>
              <!-- 回复点赞区域 -->
              <view class="reply-like-area" bindtap="onLikeReply" data-reply="{{reply}}">
                <image
                  class="reply-like-icon {{reply.isLiked ? 'liked' : ''}}"
                  src="{{reply.isLiked ? '/assets/images/detail/helpful-active.png' : '/assets/images/detail/helpful-none.png'}}"
                />
                <text class="reply-like-count {{reply.isLiked ? 'liked' : ''}}">{{reply.likeCount || 0}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      </view>
    </block>

    <!-- 无回复时显示提示 -->
    <view wx:else class="no-replies">
      <text class="no-replies-text">暂无回复</text>
    </view>

    <!-- 回复状态提示 -->
    <view class="reply-status">
      <view wx:if="{{replies.length > 0 && replies.length < totalReplyCount}}" class="load-more-replies" bindtap="onLoadMoreReplies">
        <text class="load-more-text">查看更多回复</text>
      </view>
      <view wx:elif="{{replies.length > 0 && replies.length >= totalReplyCount && totalReplyCount > 1}}" class="all-loaded-tip">
        <text class="all-loaded-text">已显示全部回复</text>
      </view>
    </view>
  </view>
</view>