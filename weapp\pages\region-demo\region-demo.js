/**
 * 地区选择功能演示页面
 */

const { regionCache } = require('../../utils/regionCache');
const { handleRegionPickerShowChange } = require('../../utils/regionPickerHelper');

Page({
  data: {
    selectedLocation: '',
    selectedLocationName: '',
    selectedRegion: null,
    selectedRegions: [],
    showRegionPicker: false,
    
    // 演示数据
    demoRegions: [],
    searchKeyword: '',
    searchResults: []
  },

  onLoad(options) {
    console.log('地区选择演示页面加载');
    this.loadCurrentLocation();
  },

  onShow() {
    // 页面显示时刷新当前位置
    this.loadCurrentLocation();
  },

  /**
   * 加载当前位置信息
   */
  loadCurrentLocation() {
    const currentLocation = regionCache.getCurrentLocation();
    if (currentLocation) {
      this.setData({
        selectedLocation: currentLocation.fullAddress,
        selectedLocationName: currentLocation.displayName,
        selectedRegion: currentLocation.region,
        selectedRegions: currentLocation.selectedRegions || []
      });
    }
  },

  /**
   * 显示地区选择器
   */
  showRegionSelector() {
    this.setData({ showRegionPicker: true });
  },

  /**
   * 地区选择确认
   */
  onRegionConfirm(e) {
    const { selectedRegions, lastRegion, fullAddress } = e.detail;
    
    console.log('选择的地区:', {
      selectedRegions,
      lastRegion,
      fullAddress
    });

    const displayName = lastRegion ? lastRegion.regionName : fullAddress;

    this.setData({
      selectedLocation: fullAddress,
      selectedLocationName: displayName,
      selectedRegion: lastRegion,
      selectedRegions: selectedRegions,
      showRegionPicker: false
    });

    // 缓存选择的位置
    const locationInfo = {
      fullAddress: fullAddress,
      displayName: displayName,
      region: lastRegion,
      selectedRegions: selectedRegions
    };
    regionCache.setCurrentLocation(locationInfo);

    wx.showToast({
      title: `已选择: ${displayName}`,
      icon: 'success'
    });
  },

  /**
   * 关闭地区选择器
   */
  onRegionClose() {
    this.setData({ showRegionPicker: false });
  },

  /**
   * 地区选择器显示状态变化
   */
  onRegionShowChange(e) {
    // 使用统一的辅助函数处理底部导航栏
    handleRegionPickerShowChange(e, {
      hideTabBar: true,
      animation: true,
      onShow: () => {
        console.log('地区选择器已显示，底部导航栏已隐藏');
      },
      onHide: () => {
        console.log('地区选择器已隐藏，底部导航栏已显示');
      }
    });
  },

  /**
   * 搜索地区
   */
  async searchRegions() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '搜索中...' });

    try {
      const results = await regionCache.searchRegions(keyword);
      this.setData({ searchResults: results });
      
      if (results.length === 0) {
        wx.showToast({
          title: '未找到相关地区',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('搜索地区失败:', error);
      wx.showToast({
        title: '搜索失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 搜索输入变化
   */
  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value });
  },

  /**
   * 选择搜索结果中的地区
   */
  selectSearchResult(e) {
    const region = e.currentTarget.dataset.region;
    
    // 构建位置信息
    const fullAddress = this.buildFullAddressFromRegion(region);
    const displayName = region.regionName;
    
    this.setData({
      selectedLocation: fullAddress,
      selectedLocationName: displayName,
      selectedRegion: region,
      searchResults: []
    });

    // 缓存选择的位置
    const locationInfo = {
      fullAddress: fullAddress,
      displayName: displayName,
      region: region
    };
    regionCache.setCurrentLocation(locationInfo);

    wx.showToast({
      title: `已选择: ${displayName}`,
      icon: 'success'
    });
  },

  /**
   * 根据地区信息构建完整地址
   */
  buildFullAddressFromRegion(region) {
    const parts = [];
    if (region.provinceName) parts.push(region.provinceName);
    if (region.cityName) parts.push(region.cityName);
    if (region.districtName) parts.push(region.districtName);
    if (region.townName) parts.push(region.townName);
    if (region.villageName) parts.push(region.villageName);
    return parts.join('');
  },

  /**
   * 清除缓存
   */
  clearCache() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有地区缓存吗？',
      success: (res) => {
        if (res.confirm) {
          regionCache.clearAllCache();
          this.setData({
            selectedLocation: '',
            selectedLocationName: '',
            selectedRegion: null,
            selectedRegions: [],
            searchResults: []
          });
          wx.showToast({
            title: '缓存已清除',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 获取当前位置（模拟）
   */
  getCurrentLocation() {
    wx.showLoading({ title: '定位中...' });
    
    // 模拟定位过程
    setTimeout(() => {
      wx.hideLoading();
      
      // 这里可以集成真实的定位服务
      // 比如腾讯地图、百度地图等
      wx.showToast({
        title: '定位功能待开发',
        icon: 'none'
      });
    }, 1000);
  },

  /**
   * 查看完整地址信息
   */
  async viewFullAddress() {
    if (!this.data.selectedRegion) {
      wx.showToast({
        title: '请先选择地区',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '加载中...' });

    try {
      const fullAddress = await regionCache.getFullAddress(this.data.selectedRegion.regionCode);
      
      if (fullAddress) {
        const content = `
省份: ${fullAddress.provinceName || '无'}
城市: ${fullAddress.cityName || '无'}
区县: ${fullAddress.districtName || '无'}
乡镇: ${fullAddress.townName || '无'}
村: ${fullAddress.villageName || '无'}
层级: ${fullAddress.level || '无'}
        `.trim();

        wx.showModal({
          title: '完整地址信息',
          content: content,
          showCancel: false
        });
      } else {
        wx.showToast({
          title: '获取地址信息失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取完整地址信息失败:', error);
      wx.showToast({
        title: '获取失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  }
});
