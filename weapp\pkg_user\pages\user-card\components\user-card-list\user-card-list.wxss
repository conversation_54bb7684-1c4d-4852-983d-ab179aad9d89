.card-list-scroll {
  width: 100vw;
  white-space: nowrap;
  overflow-x: auto;
  padding: 24rpx 0;
}

.card-list-row {
  display: flex;
  flex-direction: row;
  gap: 32rpx;
  padding-left: 24rpx;
}

.biz-card {
  width: 90vw;
  max-width: 650rpx;
  min-width: 600rpx;
  background: transparent;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  padding: 0;
  box-sizing: border-box;
  position: relative;
}

.card-face {
  width: 100%;
  min-height: 320rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.08);
  padding: 32rpx 36rpx 28rpx 36rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}



.card-back {
  background: #f8f8f8;
  align-items: center;
  justify-content: center;
}

.card-top {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.back-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 320rpx;
}

.back-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #223366;
  margin-bottom: 16rpx;
}

.back-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 24rpx;
  text-align: center;
}

.logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  background: #f5f5f5;
}

.company-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.company {
  font-size: 28rpx;
  font-weight: 600;
  color: #223366;
  letter-spacing: 2rpx;
}

.divider {
  height: 2rpx;
  background: #223366;
  margin: 18rpx 0 18rpx 0;
  opacity: 0.12;
}

.card-bottom {
  display: flex;
  flex-direction: row;
  gap: 32rpx;
}

.left-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12rpx;
}

.name {
  font-size: 36rpx;
  font-weight: bold;
  color: #223366;
  margin-bottom: 4rpx;
}

.title {
  font-size: 24rpx;
  color: #223366;
  opacity: 0.7;
  margin-bottom: 12rpx;
}

.qrcode {
  width: 120rpx;
  height: 120rpx;
  margin-top: 8rpx;
  border-radius: 12rpx;
  background: #f5f5f5;
}

.right-info {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  justify-content: center;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 24rpx;
  color: #223366;
  opacity: 0.9;
}

.iconfont {
  font-size: 28rpx;
  color: #223366;
  opacity: 0.7;
}