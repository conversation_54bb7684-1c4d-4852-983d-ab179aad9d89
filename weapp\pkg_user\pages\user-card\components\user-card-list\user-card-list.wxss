.card-list-container {
  width: 100%;
  padding: 24rpx;
}

.card-list-column {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.biz-card {
  width: 100%;
  background: transparent;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  padding: 0;
  box-sizing: border-box;
  position: relative;
}

.card-face {
  width: 100%;
  height: 480rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.08);
  padding: 32rpx 36rpx 32rpx 36rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}



.card-back {
  background: #f8f8f8;
  align-items: center;
  justify-content: center;
}

.card-top {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 20rpx;
  flex-shrink: 0;
}

.back-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 24rpx;
}

.back-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #223366;
  flex-shrink: 0;
}

.back-desc {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.6;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
  word-break: break-all;
  overflow-wrap: break-word;
}

.logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  background: #f5f5f5;
}

.company-info {
  flex: 1;
  display: flex;
  align-items: center;
}

.company {
  font-size: 28rpx;
  font-weight: 600;
  color: #223366;
  letter-spacing: 2rpx;
}

.divider {
  height: 2rpx;
  background: #223366;
  margin: 16rpx 0;
  opacity: 0.12;
  flex-shrink: 0;
}

.card-bottom {
  display: flex;
  flex-direction: row;
  gap: 32rpx;
  flex: 1;
  min-height: 0;
}

.left-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12rpx;
  min-width: 0;
}

.name {
  font-size: 36rpx;
  font-weight: bold;
  color: #223366;
  margin-bottom: 4rpx;
}

.title {
  font-size: 24rpx;
  color: #223366;
  opacity: 0.7;
  margin-bottom: 12rpx;
}

.qrcode {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  background: #f5f5f5;
  flex-shrink: 0;
}

.left-info .qrcode {
  margin-top: 8rpx;
}

.back-content .qrcode {
  width: 140rpx;
  height: 140rpx;
}

.right-info {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  justify-content: flex-start;
  min-width: 0;
  overflow: hidden;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 22rpx;
  color: #223366;
  opacity: 0.9;
  min-height: 32rpx;
  overflow: hidden;
}

.info-text {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.iconfont {
  font-size: 28rpx;
  color: #223366;
  opacity: 0.7;
}