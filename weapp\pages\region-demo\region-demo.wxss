/* 地区选择功能演示页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  color: #333;
  margin-bottom: 40rpx;
}

/* 通用区块样式 */
.current-location-section,
.selection-path-section,
.search-section,
.action-section,
.help-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #007aff;
  padding-left: 15rpx;
}

/* 当前位置显示 */
.location-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.location-info {
  flex: 1;
  margin-right: 20rpx;
}

.location-full {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.location-short {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.location-code {
  font-size: 24rpx;
  color: #999;
}

.select-btn {
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  min-width: 160rpx;
}

.select-btn:active {
  background-color: #0056cc;
}

/* 选择路径 */
.path-container {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.path-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.path-level {
  background-color: #007aff;
  color: #fff;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 15rpx;
  min-width: 60rpx;
  text-align: center;
}

.path-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-right: 15rpx;
}

.path-code {
  font-size: 24rpx;
  color: #666;
}

/* 搜索功能 */
.search-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.search-input:focus {
  border-color: #007aff;
}

.search-btn {
  background-color: #28a745;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 0 30rpx;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
}

.search-btn:active {
  background-color: #1e7e34;
}

/* 搜索结果 */
.search-results {
  border-top: 2rpx solid #e0e0e0;
  padding-top: 20rpx;
}

.results-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.result-item {
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  margin-bottom: 15rpx;
  background-color: #fff;
  transition: all 0.2s;
}

.result-item:active {
  background-color: #f0f8ff;
  border-color: #007aff;
}

.result-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.result-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.result-level {
  background-color: #6c757d;
  color: #fff;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.result-code {
  font-size: 24rpx;
  color: #666;
}

/* 功能按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  background-color: #6c757d;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 25rpx;
  font-size: 28rpx;
  text-align: center;
}

.action-btn:active {
  background-color: #545b62;
}

.action-btn.danger {
  background-color: #dc3545;
}

.action-btn.danger:active {
  background-color: #c82333;
}

/* 使用说明 */
.help-content {
  line-height: 1.6;
}

.help-item {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
  padding-left: 20rpx;
  position: relative;
}

.help-item::before {
  content: '•';
  color: #007aff;
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .location-display {
    flex-direction: column;
    align-items: stretch;
  }
  
  .location-info {
    margin-right: 0;
    margin-bottom: 20rpx;
  }
  
  .search-container {
    flex-direction: column;
  }
  
  .search-input {
    margin-bottom: 20rpx;
  }
}
