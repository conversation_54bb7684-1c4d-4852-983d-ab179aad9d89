/* pkg_user/pages/local/components/institution-card/institution-card.wxss */

.institution-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.institution-card:active {
  transform: scale(0.98);
}

/* 机构头部 */
.institution-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.institution-logo {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.logo-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.logo-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ff6b6b;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 12rpx;
}

.institution-info {
  flex: 1;
}

.institution-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.institution-tags {
  display: flex;
  gap: 8rpx;
}

.tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.category-tag {
  background: #e3f2fd;
  color: #1976d2;
}

.verified-tag {
  background: #e8f5e8;
  color: #4caf50;
}

.institution-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f8f8;
  border-radius: 50%;
  font-size: 24rpx;
  transition: background 0.2s ease;
}

.action-btn:active {
  background: #e0e0e0;
}

.phone-btn {
  background: #e8f5e8;
}

.location-btn {
  background: #fff3e0;
}

/* 机构详细信息 */
.institution-details {
  margin-bottom: 20rpx;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-top: 2rpx;
  flex-shrink: 0;
}

.detail-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.description .detail-text {
  color: #999;
  font-size: 24rpx;
}

/* 机构统计信息 */
.institution-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff6b6b;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #999;
}
