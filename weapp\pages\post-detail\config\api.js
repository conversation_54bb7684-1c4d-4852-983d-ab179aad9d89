/**
 * 帖子详情页API配置
 */

const BASE_URL = '/blade-chat';

// API端点配置
const API_ENDPOINTS = {
  // 帖子相关
  post: {
    detail: `${BASE_URL}/post/detail`,
    like: `${BASE_URL}/post/like`,
    favorite: `${BASE_URL}/post/favorite`,
    share: `${BASE_URL}/post/share`
  },

  // 反馈相关
  feedback: {
    submit: `${BASE_URL}/feedback/submit`,
    page: `${BASE_URL}/feedback/page`,
    like: `${BASE_URL}/feedback/comment/like`, // 统一使用评论点赞接口
    tags: `${BASE_URL}/feedback/getTagsByCategory`,
    hotTags: `${BASE_URL}/feedback/getHotTags`
  },

  // 评论相关
  comment: {
    add: `${BASE_URL}/feedback/comment/add`,
    reply: `${BASE_URL}/feedback/comment/reply`,
    list: `${BASE_URL}/feedback/comment/list`,
    replies: `${BASE_URL}/feedback/comment/replies`,
    remove: `${BASE_URL}/feedback/comment/remove`,
    like: `${BASE_URL}/feedback/comment/like`,
    detail: `${BASE_URL}/feedback/comment/detail`
  },

  // 举报相关
  report: {
    submit: `${BASE_URL}/report/submit`,
    types: `${BASE_URL}/report/types`
  }
};

// HTTP状态码
const HTTP_STATUS = {
  SUCCESS: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500
};

// 业务状态码
const BUSINESS_CODE = {
  SUCCESS: 200,
  ERROR: 500,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403
};

module.exports = {
  API_ENDPOINTS,
  HTTP_STATUS,
  BUSINESS_CODE
};
