const { request } = require('../utils/request');

// 获取验证码
const getVerifyCode = (phone) => {
  return request({
    url: '/api/merchant/verify-code',
    method: 'POST',
    data: { phone }
  });
};

// 上传图片（通用）
const uploadImage = (filePath) => {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: 'https://wechat.langchuanxinxi.cn/api/merchant/upload', // 替换为实际上传接口
      filePath,
      name: 'file',
      success: res => {
        const data = JSON.parse(res.data);
        if (data.code === 200) {
          resolve(data.data.url);
        } else {
          reject(data.msg);
        }
      },
      fail: reject
    });
  });
};

// 提交商家入驻表单
const submitMerchant = (form) => {
  return request({
    url: '/api/merchant/settle',
    method: 'POST',
    data: form
  });
};

module.exports = {
  getVerifyCode,
  uploadImage,
  submitMerchant
}; 