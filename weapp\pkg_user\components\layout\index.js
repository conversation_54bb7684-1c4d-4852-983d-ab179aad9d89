Component({
  properties: {
    title: {
      type: String,
      value: ''
    },
    showLocation: {
      type: Boolean,
      value: false
    },
    showBack: {
      type: Boolean,
      value: false
    },
    showSearch: {
      type: Boolean,
      value: false
    },
    background: {
      type: String,
      value: 'linear-gradient(to bottom, #ff6b6b, #ff8585)'
    },
    textColor: {
      type: String,
      value: '#ffffff'
    }
  },

  data: {
    navBackgroundColor: '',
    navTextColor: '',
    navBarHeight: 0,
  },

  attached() {
    this.setData({
      navBackgroundColor: this.data.navBackgroundColor || this.properties.background,
      navTextColor: this.data.navTextColor || this.properties.textColor
    });
    this.initNavBar();
  },

  methods: {
    initNavBar() {
      const systemInfo = wx.getSystemInfoSync();
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      const navBarHeight = (menuButtonInfo.top - systemInfo.statusBarHeight) * 2 + menuButtonInfo.height + systemInfo.statusBarHeight;
      
      this.setData({
        navBarHeight: navBarHeight
      });
    },

    handleScroll(scrollTop) {
      // 获取custom-nav组件实例
      const customNav = this.selectComponent('custom-nav');
      if (customNav) {
        customNav.handleScroll(scrollTop);
      }
    }
  },
  onPullDownRefresh() {
    console.log('下拉刷新');
  },
}); 