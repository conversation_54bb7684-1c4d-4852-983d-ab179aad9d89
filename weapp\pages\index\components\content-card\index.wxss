/* 内容卡片 */
.content-card {
  background-color: var(--card-background);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid #f0f0f0;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
}

/* 去除高亮样式 */
.content-card.highlight {
  background: #fff;
  border: 1rpx solid #f0f0f0;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  transform: none;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

/* 左侧区域 */
.header-left {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.shop-info {
  flex: 1;
}

.shop-name {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
}

.post-time {
  font-size: 26rpx;
  color: #999;
  display: flex;
  align-items: center;
}

/* 优惠信息 */
.promotion-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  margin:  0 0 24rpx 0;
}

/* 地址信息 */
.shop-address {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 24rpx;
}

/* 图片九宫格区域 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.grid-image {
  border-radius: 12rpx;
  object-fit: cover;
  background-color: #f5f5f5;
}

/* 单张图片 */
.grid-image.single {
  width: 100%;
  height: 400rpx;
}

/* 两张图片 */
.grid-image.double {
  width: calc(50% - 6rpx);
  height: 300rpx;
}

/* 多张图片（3-6张） */
.grid-image.multiple {
  width: calc(33.333% - 8rpx);
  height: 200rpx;
}

/* 底部互动栏 */
.interaction-bar {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #999;
  margin-top: 16rpx;
}

.interaction-item {
  display: flex;
  align-items: center;
  margin-right: 32rpx;
}

.interaction-item .icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  opacity: 0.7;
}

.interaction-item .icon.heart {
  color: #ff4d4f;
}

.tag-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  align-items: center;
}
.tag {
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  color: #fff;
  width: auto;
  background-color: #FF7D7D;
  border-radius: 24rpx;
}

.tag-item {
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  color: #6E6E6E;
  background: #F5F5F5;
  border: 1rpx solid #FF7D7D;
  border-radius: 24rpx;
}

/* 高亮卡片的标签样式 */
.highlight .tag {
  background: #FF6B6B;
  color: #fff;
}

.icon.clock {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
  vertical-align: middle;
} 