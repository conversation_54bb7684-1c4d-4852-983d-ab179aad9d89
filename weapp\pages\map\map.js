// pages/map/map.js
const { MapApi, MapUtils } = require('./services/mapApi');
const { getPostDetail } = require('../../stores/postStore');

Page({
  data: {
    // 地图相关
    latitude: 39.908823, // 默认北京坐标
    longitude: 116.397470,
    scale: 16,
    markers: [],

    // 弹窗相关
    showPostDetail: false,
    selectedPost: null,
    showPostList: false, // 新增：帖子列表弹窗
    postList: [], // 新增：帖子列表数据

    // 用户位置
    userLocation: null,
    locationReady: false, // 位置是否已获取

    // 地图区域
    currentRegion: null, // 当前地图区域
    searchRadius: 5000, // 搜索半径（米）
    lastLoadedRegion: null, // 上次加载数据的区域
    regionChangeThreshold: 0.001, // 区域变化阈值（约100米）

    // 帖子数据
    posts: [], // 帖子列表
    loadedRegions: [], // 已加载的区域记录

    // 加载状态
    loading: true,
    locationLoading: true, // 位置获取加载状态

    // 筛选条件
    category: '', // 分类筛选
    keyword: '', // 搜索关键词

    // 计算属性
    searchRadiusText: '5km', // 搜索范围文本
    showSearchInfo: false // 新增：控制搜索范围信息显示
  },

  onLoad(options) {
    console.log('地图页面加载');
    // 延迟获取位置，确保页面渲染完成
    setTimeout(() => {
      this.getUserLocation();
    }, 500);
  },

  onReady() {
    // 页面渲染完成，获取地图组件实例
    this.mapCtx = wx.createMapContext('map', this);
    console.log('地图组件准备完成');
  },

  // 获取用户位置
  getUserLocation() {
    this.setData({ locationLoading: true });
    wx.getLocation({
      type: 'gcj02',
      altitude: true, // 获取高度信息
      success: (res) => {
        console.log('获取位置成功:', res);

        const userLocation = {
          latitude: res.latitude,
          longitude: res.longitude,
          accuracy: res.accuracy,
          altitude: res.altitude
        };

        this.setData({
          latitude: res.latitude,
          longitude: res.longitude,
          userLocation: userLocation,
          locationReady: true,
          locationLoading: false
        });

        // 位置获取成功后立即加载附近帖子
        this.loadNearbyPosts();

   
      },
      fail: (err) => {
        console.error('获取位置失败:', err);
        this.setData({
          locationLoading: false,
          locationReady: false
        });

        // 根据错误类型显示不同提示
        let title = '位置获取失败';
        let content = '需要获取您的位置信息来显示附近的帖子';

        if (err.errMsg.includes('auth deny')) {
          title = '位置权限被拒绝';
          content = '请在设置中开启位置权限，以便为您显示附近的帖子';
        } else if (err.errMsg.includes('timeout')) {
          title = '定位超时';
          content = '定位超时，请检查网络连接或稍后重试';
        }

        wx.showModal({
          title: title,
          content: content,
          confirmText: '去设置',
          cancelText: '使用默认',
          success: (modalRes) => {
            if (modalRes.confirm) {
              wx.openSetting({
                success: (settingRes) => {
                  if (settingRes.authSetting['scope.userLocation']) {
                    // 用户重新授权后重新获取位置
                    this.getUserLocation();
                  }
                }
              });
            } else {
              // 使用默认位置（北京）加载数据
              this.setData({
                userLocation: {
                  latitude: this.data.latitude,
                  longitude: this.data.longitude
                },
                locationReady: true
              });
              this.loadNearbyPosts();
            }
          }
        });
      }
    });
  },

  // 加载附近的帖子
  async loadNearbyPosts() {
    if (!this.data.userLocation) {
      console.warn('用户位置未获取，无法加载附近帖子');
      return;
    }

    try {
      this.setData({ loading: true });

      // 根据地图缩放级别计算搜索半径
      const radius = MapUtils.getRadiusByScale(this.data.scale);

      const params = {
        latitude: this.data.userLocation.latitude,
        longitude: this.data.userLocation.longitude,
        radius: radius,
        current: 1,
        size: 100, // 地图显示更多帖子
        category: this.data.category,
        keyword: this.data.keyword
      };

      console.log('加载附近帖子参数:', params);

      // 调用真实API
      const result = await MapApi.getNearbyPosts(params);

      if (result && result.data && result.data.records) {
        const posts = this.processPostsData(result.data.records);
        const markers = this.generateMarkers(posts);

        // 构建当前区域信息
        const currentRegion = {
          centerLocation: {
            latitude: this.data.userLocation.latitude,
            longitude: this.data.userLocation.longitude
          },
          scale: this.data.scale
        };

        this.setData({
          posts: posts,
          markers: markers,
          loading: false,
          searchRadius: radius,
          lastLoadedRegion: currentRegion
        });

        // 更新搜索范围文本
        this.updateSearchRadiusText(radius);

        console.log(`加载了 ${posts.length} 个附近帖子`);
      } else {
        // 如果API返回空数据，使用模拟数据作为降级
        console.warn('API返回空数据，使用模拟数据');
      }
    } catch (error) {
      console.error('加载附近帖子失败:', error);

      // API失败时使用模拟数据作为降级
      console.log('API调用失败，使用模拟数据作为降级');
    }
  },

  // 处理帖子数据
  processPostsData(rawPosts) {
    return rawPosts.map(post => {
      // 计算距离
      let distance = '';
      if (this.data.userLocation && post.latitude && post.longitude) {
        const distanceInMeters = MapUtils.calculateDistance(
          this.data.userLocation.latitude,
          this.data.userLocation.longitude,
          post.latitude,
          post.longitude
        );
        distance = MapUtils.formatDistance(distanceInMeters);
      }

      return {
        id: post.id,
        title: post.title || '无标题',
        content: post.content || '',
        latitude: post.latitude,
        longitude: post.longitude,
        author: post.user.nickname || '匿名用户',
        avatar: post.user.avatar || '/assets/images/avatar/default.png',
        likeCount: post.likeCount || 0,
        commentCount: post.commentCount || 0,
        createTime: post.createTime || new Date().toLocaleDateString(),
        images: post.images ? post.images.split(',').filter(img => img.trim()) : [],
        distance: distance,
        category: post.category.name || '',
        isLiked: post.isLiked || false,
        isFavorited: post.isFavorited || false
      };
    });
  },

  // 生成地图标记点
  generateMarkers(posts) {
    return posts.map((post, index) => {
      // 确保marker id是数字类型，使用索引作为markerId
      const markerId = index + 1;

      return {
        id: markerId, // 使用数字ID
        latitude: post.latitude,
        longitude: post.longitude,
        iconPath: '/assets/images/map/marker.png',
        width: 40,
        height: 40,
        // 在data中存储真实的帖子ID
        data: {
          postId: post.id,
          postIndex: index
        },
        callout: {
          content: post.title,
          color: '#333',
          fontSize: 12,
          borderRadius: 8,
          bgColor: '#fff',
          padding: 8,
          display: 'BYCLICK'
        },
        customCallout: {
          anchorY: 0,
          anchorX: 0,
          display: 'BYCLICK'
        }
      };
    });
  },

  // 点击标记点
  onMarkerTap(e) {
    console.log('标记点击事件:', e);

    // 阻止事件冒泡到地图
    if (e.stopPropagation) {
      e.stopPropagation();
    }

    let markerId, post;
    
    // 判断是从地图标记点击还是从帖子列表点击
    if (e.detail && e.detail.markerId) {
      // 从地图标记点击
      markerId = e.detail.markerId;
      console.log('点击地图标记:', markerId, '类型:', typeof markerId);

      // 通过markerId找到对应的帖子索引（markerId = index + 1）
      const postIndex = markerId - 1;
      post = this.data.posts[postIndex];

      console.log('帖子索引:', postIndex);
      console.log('找到的帖子:', post);
    } else if (e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.markerId) {
      // 从帖子列表点击
      markerId = e.currentTarget.dataset.markerId;
      console.log('点击帖子列表项:', markerId);
      
      // 直接通过ID查找帖子
      post = this.data.posts.find(p => p.id == markerId);
      console.log('找到的帖子:', post);
    }

    if (!post) {
      console.error('未找到对应的帖子数据，markerId:', markerId);
      console.log('当前帖子总数:', this.data.posts.length);
      return;
    }

    // 设置标记，防止地图点击事件关闭弹窗
    this.markerTapTime = Date.now();

    // 隐藏帖子列表弹窗
    this.hidePostList();

    // 使用真实的帖子ID获取详情
    this.getPostDetail(post.id);
  },

  // 地图区域变化
  onRegionChange(e) {
    if (e.type === 'begin') {
      // 开始拖拽时清除定时器
      if (this.regionChangeTimer) {
        clearTimeout(this.regionChangeTimer);
      }
    } else if (e.type === 'end') {
      const region = e.detail;
      console.log('地图区域变化结束:', region);

      // 更新当前区域信息
      this.setData({
        latitude: region.centerLocation.latitude,
        longitude: region.centerLocation.longitude,
        scale: region.scale,
        currentRegion: region
      });

      // 检查是否需要加载新数据
      const shouldLoad = this.shouldLoadNewData(region);
      console.log('是否需要加载新数据:', shouldLoad);

      if (shouldLoad) {
        console.log('准备加载新数据...');
        // 防抖处理，避免频繁请求
        if (this.regionChangeTimer) {
          clearTimeout(this.regionChangeTimer);
        }

        this.regionChangeTimer = setTimeout(() => {
          console.log('开始加载区域数据...');
          this.loadPostsInRegion(region);
        }, 800); // 0.8秒后执行，提升响应速度
      } else {
        console.log('区域变化不足，跳过数据加载');
      }
    }
  },

  // 判断是否需要加载新数据
  shouldLoadNewData(newRegion) {
    const lastRegion = this.data.lastLoadedRegion;

    // 如果没有上次加载的区域，需要加载
    if (!lastRegion) {
      return true;
    }

    // 计算中心点距离变化
    const centerDistance = MapUtils.calculateDistance(
      lastRegion.centerLocation.latitude,
      lastRegion.centerLocation.longitude,
      newRegion.centerLocation.latitude,
      newRegion.centerLocation.longitude
    );

    // 计算缩放级别变化
    const scaleChange = Math.abs(newRegion.scale - lastRegion.scale);

    // 根据当前搜索半径计算阈值
    const currentRadius = MapUtils.getRadiusByScale(newRegion.scale);
    const distanceThreshold = currentRadius * 0.2; // 移动超过搜索半径的20%时重新加载
    const scaleThreshold = 1.5; // 缩放级别变化超过1.5级时重新加载

    console.log('区域变化检查:', {
      centerDistance: centerDistance,
      distanceThreshold: distanceThreshold,
      scaleChange: scaleChange,
      scaleThreshold: scaleThreshold,
      shouldLoad: centerDistance > distanceThreshold || scaleChange >= scaleThreshold
    });

    // 如果中心点移动超过阈值，或缩放级别变化超过阈值，则重新加载
    return centerDistance > distanceThreshold || scaleChange >= scaleThreshold;
  },

  // 根据地图区域加载帖子
  async loadPostsInRegion(region) {
    try {
      this.setData({ loading: true });

      // 计算地图边界
      const bounds = MapUtils.getMapBounds(
        region.centerLocation.latitude,
        region.centerLocation.longitude,
        region.scale
      );

      // 根据缩放级别计算搜索半径
      const radius = MapUtils.getRadiusByScale(region.scale);

      const params = {
        centerLat: region.centerLocation.latitude,
        centerLng: region.centerLocation.longitude,
        northeastLat: bounds.northeastLat,
        northeastLng: bounds.northeastLng,
        southwestLat: bounds.southwestLat,
        southwestLng: bounds.southwestLng,
        radius: radius,
        current: 1,
        size: 100,
        category: this.data.category,
        keyword: this.data.keyword
      };

      console.log('根据地图区域加载帖子:', params);

      // 调用API获取区域内帖子
      const result = await MapApi.getPostsInRegion(params);

      if (result && result.data && result.data.records) {
        const posts = this.processPostsData(result.data.records);
        const markers = this.generateMarkers(posts);

        // 记录本次加载的区域
        const loadedRegions = [...this.data.loadedRegions];
        loadedRegions.push({
          center: region.centerLocation,
          scale: region.scale,
          radius: radius,
          timestamp: Date.now(),
          postCount: posts.length
        });

        // 只保留最近10次的加载记录
        if (loadedRegions.length > 10) {
          loadedRegions.shift();
        }

        this.setData({
          posts: posts,
          markers: markers,
          loading: false,
          searchRadius: radius,
          lastLoadedRegion: region,
          loadedRegions: loadedRegions
        });

        // 更新搜索范围文本
        this.updateSearchRadiusText(radius);

        console.log(`区域内加载了 ${posts.length} 个帖子，中心位置: (${region.centerLocation.latitude.toFixed(6)}, ${region.centerLocation.longitude.toFixed(6)})`);

        // 显示加载成功提示
        wx.showToast({
          title: `找到 ${posts.length} 个帖子`,
          icon: 'none',
          duration: 1500
        });

      } else {
        // 降级到附近帖子
        console.warn('区域API返回空数据，降级到附近帖子');
        this.loadNearbyPostsForRegion(region);
      }
    } catch (error) {
      console.error('根据区域加载帖子失败:', error);

      // 降级到附近帖子
      console.log('区域API调用失败，降级到附近帖子');
      this.loadNearbyPostsForRegion(region);
    }
  },

  // 为特定区域加载附近帖子（降级方案）
  async loadNearbyPostsForRegion(region) {
    try {
      const radius = MapUtils.getRadiusByScale(region.scale);

      const params = {
        latitude: region.centerLocation.latitude,
        longitude: region.centerLocation.longitude,
        radius: radius,
        current: 1,
        size: 100,
        category: this.data.category,
        keyword: this.data.keyword
      };

      console.log('为区域加载附近帖子:', params);

      const result = await MapApi.getNearbyPosts(params);

      if (result && result.data && result.data.records) {
        const posts = this.processPostsData(result.data.records);
        const markers = this.generateMarkers(posts);

        this.setData({
          posts: posts,
          markers: markers,
          loading: false,
          searchRadius: radius,
          lastLoadedRegion: region
        });

        // 更新搜索范围文本
        this.updateSearchRadiusText(radius);

        console.log(`附近帖子加载了 ${posts.length} 个帖子`);
      } else {
        // 最后降级到模拟数据
        console.warn('附近帖子API也返回空数据，使用模拟数据');
      }
    } catch (error) {
      console.error('加载附近帖子失败:', error);
      // 最后降级到模拟数据
    }
  },


  // 点击地图空白区域
  onMapTap(e) {
    console.log('地图点击事件:', e.detail);

    // 如果刚刚点击了标记（100ms内），则不关闭弹窗
    if (this.markerTapTime && (Date.now() - this.markerTapTime) < 100) {
      console.log('刚刚点击了标记，忽略地图点击');
      return;
    }

    // 关闭帖子详情弹窗
    if (this.data.showPostDetail) {
      console.log('点击地图空白区域，关闭弹窗');
      this.onClosePostDetail();
    }
  },

  // 获取帖子详情
  async getPostDetail(postId) {
    try {
      console.log('获取帖子详情:', postId, '类型:', typeof postId);

      // 首先从本地数据中查找
      const localPost = this.data.posts.find(p => p.id == postId); // 使用==而不是===，避免类型问题
      console.log('本地查找结果:', localPost);

      if (localPost) {
        console.log('使用本地数据显示帖子详情');
        console.log('设置弹窗数据:', {
          selectedPost: localPost,
          showPostDetail: true
        });

        // 使用setTimeout确保弹窗能正确显示
        setTimeout(() => {
          this.setData({
            selectedPost: localPost,
            showPostDetail: true
          }, () => {
            console.log('弹窗数据设置完成:', {
              showPostDetail: this.data.showPostDetail,
              selectedPost: this.data.selectedPost
            });
          });
        }, 50); // 延迟50ms，避免与地图点击事件冲突
        return;
      }

      console.log('本地未找到，尝试从API获取');


      const result = await getPostDetail(postId);


      if (result && result.data) {
        const postDetail = this.processPostsData([result.data])[0];
        this.setData({
          selectedPost: postDetail,
          showPostDetail: true
        });
      } else {
        wx.showToast({
          title: '帖子不存在',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取帖子详情失败:', error);
      // 降级方案：从本地数据中查找
      const localPost = this.data.posts.find(p => p.id === postId);
      if (localPost) {
        this.setData({
          selectedPost: localPost,
          showPostDetail: true
        });
      } else {
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      }
    }
  },

  // 关闭帖子详情弹窗
  onClosePostDetail() {
    console.log('关闭帖子详情弹窗');
    this.setData({
      showPostDetail: false,
      selectedPost: null
    });
  },

  // 测试弹窗显示（调试用）
  testShowModal() {
    console.log('测试显示弹窗');
    const testPost = {
      id: 'test-123',
      title: '测试帖子标题',
      content: '这是一个测试帖子的内容',
      author: '测试用户',
      avatar: '/assets/images/avatar/default.png',
      createTime: '2024-01-01',
      likeCount: 10,
      commentCount: 5,
      distance: '100m',
      category: '测试分类'
    };

    this.setData({
      selectedPost: testPost,
      showPostDetail: true
    }, () => {
      console.log('测试弹窗设置完成:', {
        showPostDetail: this.data.showPostDetail,
        selectedPost: this.data.selectedPost
      });
    });
  },

  // 点赞帖子
  async onLikePost() {
    if (!this.data.selectedPost) return;

    const post = this.data.selectedPost;
    const newLikeStatus = !post.isLiked;

    try {
      // 先更新UI，提供即时反馈
      const updatedPost = {
        ...post,
        isLiked: newLikeStatus,
        likeCount: newLikeStatus ? post.likeCount + 1 : post.likeCount - 1
      };

      this.setData({
        selectedPost: updatedPost
      });

      // 更新本地帖子列表中的数据
      const posts = this.data.posts.map(p =>
        p.id === post.id ? updatedPost : p
      );
      this.setData({ posts });

      // 调用API
      await MapApi.toggleLike(post.id, newLikeStatus);

      wx.showToast({
        title: newLikeStatus ? '点赞成功' : '取消点赞',
        icon: 'success',
        duration: 1500
      });

    } catch (error) {
      console.error('点赞操作失败:', error);

      // 恢复原状态
      this.setData({
        selectedPost: post
      });

      const posts = this.data.posts.map(p =>
        p.id === post.id ? post : p
      );
      this.setData({ posts });

      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },

  // 查看帖子详情
  onViewPostDetail() {
    if (!this.data.selectedPost) return;
    
    // 跳转到帖子详情页
    wx.navigateTo({
      url: `/pages/post-detail/post-detail?id=${this.data.selectedPost.id}`
    });
  },

  // 刷新地图数据
  onRefresh() {
    console.log('刷新地图数据');

    wx.showToast({
      title: '刷新中...',
      icon: 'loading',
      duration: 1000
    });

    // 如果有当前区域信息，优先使用区域加载
    if (this.data.currentRegion) {
      this.loadPostsInRegion(this.data.currentRegion);
    } else {
      // 否则使用附近帖子加载
      this.loadNearbyPosts();
    }
  },

  // 更新搜索范围文本
  updateSearchRadiusText(radius) {
    const radiusText = radius >= 1000 ?
      `${(radius / 1000).toFixed(1)}km` :
      `${radius}m`;

    this.setData({
      searchRadiusText: radiusText,
      showSearchInfo: true
    });

    // 1秒后隐藏搜索范围信息
    setTimeout(() => {
      this.setData({
        showSearchInfo: false
      });
    }, 1000);
  },

  // 设置筛选条件
  setFilter(category = '', keyword = '') {
    this.setData({
      category: category,
      keyword: keyword
    });

    // 重新加载数据
    this.onRefresh();
  },

  // 获取当前地图状态信息
  getMapStatusInfo() {
    const region = this.data.currentRegion;
    const lastLoaded = this.data.lastLoadedRegion;

    if (!region) return '地图未初始化';

    const centerInfo = `中心: (${region.centerLocation.latitude.toFixed(6)}, ${region.centerLocation.longitude.toFixed(6)})`;
    const scaleInfo = `缩放: ${region.scale}`;
    const radiusInfo = `范围: ${this.data.searchRadius >= 1000 ? (this.data.searchRadius/1000).toFixed(1) + 'km' : this.data.searchRadius + 'm'}`;
    const postInfo = `帖子: ${this.data.posts.length}个`;

    let loadInfo = '';
    if (lastLoaded) {
      const distance = MapUtils.calculateDistance(
        region.centerLocation.latitude,
        region.centerLocation.longitude,
        lastLoaded.centerLocation.latitude,
        lastLoaded.centerLocation.longitude
      );
      loadInfo = `距上次加载: ${MapUtils.formatDistance(distance)}`;
    }

    return `${centerInfo}\n${scaleInfo} | ${radiusInfo} | ${postInfo}\n${loadInfo}`;
  },

  // 显示地图状态信息（调试用）
  showMapStatus() {
    const statusInfo = this.getMapStatusInfo();
    console.log('地图状态信息:', statusInfo);

    wx.showModal({
      title: '地图状态',
      content: statusInfo,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 显示帖子列表
  showPostList() {
    this.setData({
      showPostList: true,
      postList: this.data.posts
    });
  },

  // 隐藏帖子列表
  hidePostList() {
    this.setData({
      showPostList: false
    });
  },

  // 回到用户位置
  onBackToUserLocation() {
    if (this.data.userLocation && this.mapCtx) {
      // 使用地图API平滑移动到用户位置
      this.mapCtx.moveToLocation({
        latitude: this.data.userLocation.latitude,
        longitude: this.data.userLocation.longitude,
        success: () => {
          console.log('移动到用户位置成功');
          // 更新数据以触发重新渲染
          this.setData({
            latitude: this.data.userLocation.latitude,
            longitude: this.data.userLocation.longitude
          });

          wx.showToast({
            title: '已回到当前位置',
            icon: 'success',
            duration: 1500
          });
        },
        fail: (err) => {
          console.error('移动到用户位置失败:', err);
          // 降级方案：直接设置坐标
          this.setData({
            latitude: this.data.userLocation.latitude,
            longitude: this.data.userLocation.longitude
          });
        }
      });
    } else if (!this.data.userLocation) {
      // 如果没有用户位置，重新获取
      wx.showToast({
        title: '正在重新定位...',
        icon: 'loading'
      });
      this.getUserLocation();
    } else {
      // 地图组件未准备好，直接设置坐标
      this.setData({
        latitude: this.data.userLocation.latitude,
        longitude: this.data.userLocation.longitude
      });
    }
  }
});
