Component({
  properties: {
    menus: {
      type: Array,
      value: [
        { icon: '/assets/images/mine/signin.png', text: '每日签到', url: '/pages/mine/signin/signin' },
        { icon: '/assets/images/mine/gift.png', text: '积分商城', url: '/pages/mine/points/points' },
        { icon: '/assets/images/mine/shield.png', text: '隐私设置', url: '/pages/mine/privacy/privacy' },
        { icon: '/assets/images/mine/bell.png', text: '消息中心', url: '/pages/mine/notification/notification' },
        { icon: '/assets/images/mine/feedback.png', text: '程序反馈', url: '/pages/mine/feedback/feedback' }
      ]
    }
  },
  methods: {
    onMenuTap(e) {
      const url = e.currentTarget.dataset.url;
      this.triggerEvent('navigate', { url });
    }
  }
}); 