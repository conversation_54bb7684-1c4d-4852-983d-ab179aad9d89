/* my-post-item.wxss */
.post-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: visible;
}

.post-item:active {
  background: #f9f9f9;
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10rpx;
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12rpx;
}

.nickname {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.post-time {
  font-size: 24rpx;
  color: #999;
}

/* 分类和标签样式 */
.category-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.category-tag {
  padding: 6rpx 16rpx;
  background: rgba(76, 111, 255, 0.1);
  color: #4C6FFF;
  font-size: 22rpx;
  border-radius: 6rpx;
  border: 1rpx solid rgba(76, 111, 255, 0.2);
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.tag-item {
  padding: 6rpx 12rpx;
  color: #4C6FFF;
  font-size: 22rpx;
  border-radius: 6rpx;
  background: rgba(76, 111, 255, 0.1);
  border: 1rpx solid rgba(76, 111, 255, 0.2);
}

.post-content {
  position: relative;
  margin-bottom: 16rpx;
}

.post-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  word-break: break-all;
}

.post-images {
  margin-top: 16rpx;
}

.image-grid {
  display: flex;
  gap: 8rpx;
}

.image-item {
  flex: 1;
  height: 220rpx;
  position: relative;
  overflow: hidden;
  border-radius: 8rpx;
}

.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.more-images {
  position: relative;
}

.more-count {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
}

.post-footer {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.post-stats {
  display: flex;
  gap: 24rpx;
  flex: 1;
}

.stat-item {
  font-size: 24rpx;
  color: #666;
}

.stat-item.active {
  color: #4C6FFF;
}

.stat-item.completed {
  color: #07c160;
}

.post-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 6rpx 16rpx;
  font-size: 24rpx;
  color: #4C6FFF;
  background: rgba(76, 111, 255, 0.1);
  border-radius: 6rpx;
}

.action-btn.cancel-btn,
.action-btn.cancel-like-btn {
  color: #999;
  background: #f5f5f5;
}

.action-btn.completed-btn {
  color: #07c160;
  background: rgba(7, 193, 96, 0.1);
}

.delete-btn {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

.post-status {
  font-size: 24rpx;
}

.status-text {
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.status-passed {
  color: #07c160;
  background: rgba(7, 193, 96, 0.1);
}

.status-pending {
  color: #ff9800;
  background: rgba(255, 152, 0, 0.1);
}

/* 添加空状态样式 */
.empty, .no-more, .loading {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 28rpx;
}

.empty {
  padding: 100rpx 0;
}

/* 已完成图片 */
.completed-image {
  position: absolute;
  top: 100%;
  right: 20rpx;
  width: 140rpx;
  height: 140rpx;
  z-index: 2;
  opacity: 0.95;
} 