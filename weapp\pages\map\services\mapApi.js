/**
 * 地图页面API服务
 */
const { request } = require('../../../utils/request');

/**
 * 地图相关API
 */
class MapApi {
  /**
   * 获取地图范围内的帖子
   * @param {Object} params 查询参数
   * @param {number} params.centerLat 中心纬度
   * @param {number} params.centerLng 中心经度
   * @param {number} params.northeastLat 东北角纬度
   * @param {number} params.northeastLng 东北角经度
   * @param {number} params.southwestLat 西南角纬度
   * @param {number} params.southwestLng 西南角经度
   * @param {number} params.radius 搜索半径（米）
   * @param {number} params.current 当前页码
   * @param {number} params.size 每页大小
   * @param {string} params.category 分类ID
   * @param {string} params.keyword 搜索关键词
   * @returns {Promise} 帖子列表
   */
  static async getPostsInRegion(params = {}) {
    try {
      const response = await request({
        url: '/blade-chat-open/post/map-list',
        method: 'GET',
        data: {
          centerLat: params.centerLat,
          centerLng: params.centerLng,
          northeastLat: params.northeastLat,
          northeastLng: params.northeastLng,
          southwestLat: params.southwestLat,
          southwestLng: params.southwestLng,
          radius: params.radius || 5000, // 默认5公里
          current: params.current || 1,
          size: params.size || 50, // 地图显示更多数据
          category: params.category || '',
          keyword: params.keyword || '',
          sortType: 'distance' // 按距离排序
        }
      });

      return response;
    } catch (error) {
      console.error('获取地图帖子列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取附近的帖子（基于用户位置）
   * @param {Object} params 查询参数
   * @param {number} params.latitude 用户纬度
   * @param {number} params.longitude 用户经度
   * @param {number} params.radius 搜索半径（米）
   * @param {number} params.current 当前页码
   * @param {number} params.size 每页大小
   * @returns {Promise} 附近帖子列表
   */
  static async getNearbyPosts(params = {}) {
    try {
      const response = await request({
        url: '/blade-chat-open/post/home-list',
        method: 'GET',
        data: {
          latitude: params.latitude,
          longitude: params.longitude,
          radius: params.radius || 5000, // 默认5公里
          current: params.current || 1,
          size: params.size || 50,
          keyword: params.keyword || '',
          sortType: 'distance'
        }
      });

      return response;
    } catch (error) {
      console.error('获取附近帖子列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取帖子详情
   * @param {string} postId 帖子ID
   * @returns {Promise} 帖子详情
   */
  static async getPostDetail(postId) {
    try {
      const response = await request({
        url: '/blade-chat-open/post/detail',
        method: 'GET',
        data: { id: postId }
      });

      return response;
    } catch (error) {
      console.error('获取帖子详情失败:', error);
      throw error;
    }
  }

  /**
   * 点赞帖子
   * @param {string} postId 帖子ID
   * @param {boolean} isLike 是否点赞
   * @returns {Promise} 操作结果
   */
  static async toggleLike(postId, isLike) {
    try {
      const response = await request({
        url: '/blade-chat/post/like',
        method: 'POST',
        data: {
          postId: postId,
          isLike: isLike
        }
      });

      return response;
    } catch (error) {
      console.error('点赞操作失败:', error);
      throw error;
    }
  }

  /**
   * 收藏帖子
   * @param {string} postId 帖子ID
   * @param {boolean} isFavorite 是否收藏
   * @returns {Promise} 操作结果
   */
  static async toggleFavorite(postId, isFavorite) {
    try {
      const response = await request({
        url: '/blade-chat/post/favorite',
        method: 'POST',
        data: {
          postId: postId,
          isFavorite: isFavorite
        }
      });

      return response;
    } catch (error) {
      console.error('收藏操作失败:', error);
      throw error;
    }
  }
}

/**
 * 地图工具类
 */
class MapUtils {
  /**
   * 计算两点之间的距离（米）
   * @param {number} lat1 纬度1
   * @param {number} lng1 经度1
   * @param {number} lat2 纬度2
   * @param {number} lng2 经度2
   * @returns {number} 距离（米）
   */
  static calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371000; // 地球半径（米）
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * 角度转弧度
   * @param {number} degrees 角度
   * @returns {number} 弧度
   */
  static toRadians(degrees) {
    return degrees * (Math.PI / 180);
  }

  /**
   * 格式化距离显示
   * @param {number} distance 距离（米）
   * @returns {string} 格式化后的距离
   */
  static formatDistance(distance) {
    if (distance < 1000) {
      return `${Math.round(distance)}m`;
    } else {
      return `${(distance / 1000).toFixed(1)}km`;
    }
  }

  /**
   * 根据地图缩放级别计算搜索半径
   * @param {number} scale 地图缩放级别
   * @returns {number} 搜索半径（米）
   */
  static getRadiusByScale(scale) {
    // 根据缩放级别动态调整搜索半径
    if (scale >= 18) return 500;    // 500米
    if (scale >= 16) return 1000;   // 1公里
    if (scale >= 14) return 2000;   // 2公里
    if (scale >= 12) return 5000;   // 5公里
    if (scale >= 10) return 10000;  // 10公里
    return 20000; // 20公里
  }

  /**
   * 计算地图可视区域的边界
   * @param {number} centerLat 中心纬度
   * @param {number} centerLng 中心经度
   * @param {number} scale 缩放级别
   * @returns {Object} 边界坐标
   */
  static getMapBounds(centerLat, centerLng, scale) {
    // 根据缩放级别更精确地计算可视范围
    // 微信小程序地图的缩放级别范围通常是3-20
    // scale越大，显示的区域越小，精度越高

    // 基于缩放级别计算可视范围（经验公式）
    const zoomFactor = Math.pow(2, 18 - scale); // 缩放因子
    const baseLatDelta = 0.0001 * zoomFactor; // 基础纬度范围
    const baseLngDelta = 0.0001 * zoomFactor; // 基础经度范围

    // 考虑纬度对经度的影响（纬度越高，经度间距越小）
    const latRadians = centerLat * Math.PI / 180;
    const lngDelta = baseLngDelta / Math.cos(latRadians);

    // 添加一定的缓冲区，确保边界外也能加载到帖子
    const bufferFactor = 1.5;
    const latDelta = baseLatDelta * bufferFactor;
    const adjustedLngDelta = lngDelta * bufferFactor;

    return {
      northeastLat: centerLat + latDelta,
      northeastLng: centerLng + adjustedLngDelta,
      southwestLat: centerLat - latDelta,
      southwestLng: centerLng - adjustedLngDelta
    };
  }

  /**
   * 检查两个区域是否重叠
   * @param {Object} region1 区域1
   * @param {Object} region2 区域2
   * @returns {boolean} 是否重叠
   */
  static isRegionOverlap(region1, region2) {
    return !(region1.northeastLat < region2.southwestLat ||
             region1.southwestLat > region2.northeastLat ||
             region1.northeastLng < region2.southwestLng ||
             region1.southwestLng > region2.northeastLng);
  }

  /**
   * 计算区域的面积（平方米）
   * @param {Object} bounds 区域边界
   * @returns {number} 面积（平方米）
   */
  static calculateRegionArea(bounds) {
    const latDiff = bounds.northeastLat - bounds.southwestLat;
    const lngDiff = bounds.northeastLng - bounds.southwestLng;

    // 简化计算：1度纬度约111km，1度经度约111km*cos(纬度)
    const centerLat = (bounds.northeastLat + bounds.southwestLat) / 2;
    const latDistance = latDiff * 111000; // 米
    const lngDistance = lngDiff * 111000 * Math.cos(centerLat * Math.PI / 180); // 米

    return latDistance * lngDistance; // 平方米
  }
}

module.exports = {
  MapApi,
  MapUtils
};
