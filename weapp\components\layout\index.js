Component({
  properties: {
    title: {
      type: String,
      value: ''
    },
    showLocation: {
      type: Boolean,
      value: false
    },
    showBack: {
      type: Boolean,
      value: false
    },
    showSearch: {
      type: Boolean,
      value: false
    },
    background: {
      type: String,
      value: 'linear-gradient(to bottom, #ff6b6b, #ff8585)'
    },
    textColor: {
      type: String,
      value: '#ffffff'
    }
  },

  data: {
    navBackgroundColor: '',
    navTextColor: '',
    navBarHeight: 0,
    lastScrollTop: 0,
    hideTabbar: false
  },

  attached() {
    this.setData({
      navBackgroundColor: this.data.navBackgroundColor || this.properties.background,
      navTextColor: this.data.navTextColor || this.properties.textColor
    });
    this.initNavBar();
  },

  methods: {
    initNavBar() {
      const systemInfo = wx.getSystemInfoSync();
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      const navBarHeight = (menuButtonInfo.top - systemInfo.statusBarHeight) * 2 + menuButtonInfo.height + systemInfo.statusBarHeight;
      this.setData({
        navBarHeight: navBarHeight
      });
    },

    handleScroll(scrollTop) {
      const delta = scrollTop - this.data.lastScrollTop;
      if (delta > 10 && scrollTop > 50) {
        // 向下滑动，隐藏
        if (!this.data.hideTabbar) {
          this.setData({ hideTabbar: true });
        }
      } else if (delta < -10) {
        // 向上滑动，显示
        if (this.data.hideTabbar) {
          this.setData({ hideTabbar: false });
        }
      }
      this.setData({ lastScrollTop: scrollTop });
    }
  },
  onPullDownRefresh() {
    console.log('下拉刷新');
  },
}); 