// mine.js
const { getUserInfo, logout, request } = require('../../utils/auth');
const { getQuickAccessIndex } = require('../../stores/indexStore');

Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    canIUseGetUserProfile: false,
    stats: {
      points: 2580,  // 积分
      favorites: 46, // 收藏数
      posts: 128,    // 发布数
      weeklyPosts: 5, // 本周发布数
      exposureGrowth: 30, // 曝光增长率
      taskProgress: 60, // 任务进度(百分比)
      remainingTasks: 3  // 剩余任务数
    },
    posts: [
    ],
    userBadges: ['活跃发布者', '社区达人'],
    // 新增layout相关数据
    navBarHeight: 88, // 默认导航栏高度
    menuButtonHeight: 0,
    showTabbar: true,
    lastScrollTop: 0,
    isRefreshing: false,
    menus: [] // 新增菜单数据
  },

  // 测试用
  goToUserDetail() {
    wx.navigateTo({
      url: '/pages/mine/signin/signin'
    })
  },

  onLoad: function() {
    // 判断是否可以使用 getUserProfile
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      });
    }
    this.checkLogin();
    this.loadMenus(); // 新增：加载菜单
    
    // 获取custom-nav组件实例
    this.customNav = this.selectComponent('#custom-nav');
  },

  // 新增：加载菜单方法
  loadMenus: async function() {
    try {
      const menus = await getQuickAccessIndex();
      this.setData({ menus });
      console.log(this.data.menus)
    } catch (e) {
      this.setData({ menus: [] });
    }
  },

  // 导航栏准备完成事件
  onNavReady(e) {
    const navHeight = e.detail.height;
    const menuButtonHeight = e.detail.menuButtonHeight;
    console.log('导航栏信息:', e.detail);
    this.setData({
      navBarHeight: navHeight,
      menuButtonHeight: menuButtonHeight
    });
    console.log('导航栏高度:', navHeight);
  },

  // 滚动事件处理
  onScroll(e) {
    const scrollTop = e.detail.scrollTop;
    // 控制tabbar显隐
    if (scrollTop > this.data.lastScrollTop + 10) {
      this.setData({ showTabbar: false });
    } else if (scrollTop < this.data.lastScrollTop - 10) {
      this.setData({ showTabbar: true });
    }
    this.data.lastScrollTop = scrollTop;

    // 关键：同步传递给custom-nav组件
    if (this.customNav && this.customNav.handleScroll) {
      this.customNav.handleScroll(scrollTop);
    }
  },

  onShow: function() {
    this.checkLogin();
    this.getUserStats();
    this.getMyPosts();
  },

  // 检查登录状态
  checkLogin() {
    const userInfo = getUserInfo();
    console.log('检查 用户登录状态 userInfo', userInfo);
    if (userInfo) {
      this.setData({
        userInfo,
        hasUserInfo: true
      });
    } else {
      this.setData({
        userInfo: {},
        hasUserInfo: false
      });
    }
  },

  // 去登录
  goLogin: function() {
    if (this.data.canIUseGetUserProfile) {
      // 跳转登录页面
      wx.navigateTo({
        url: '/pages/login/login'
      });
    } else {
      // 兼容处理
      wx.showToast({
        title: '请更新微信版本',
        icon: 'none'
      });
    }
  },

  // 检查登录并跳转
  checkLoginAndNavigate: function(url) {
    if (!this.data.hasUserInfo) {
      console.log('未登录，跳转到登录页面');
      wx.navigateTo({
        url: '/pages/login/login'
      });
      return false;
    }
    console.log('登录，跳转到', url);
    wx.navigateTo({ url });
    return true;
  },

  // 编辑资料
  editProfile: function() {
    // 跳转到我的资料页面
    console.log('编辑资料');
    this.checkLoginAndNavigate('/pages/mine/profile/index');
  },

  // 设置
  goSettings: function() {
    this.checkLoginAndNavigate('/pages/mine/profile/profile');
  },

  // 我的帖子
  goMyPosts: function() {
    this.checkLoginAndNavigate('/pages/mine/posts/posts');
  },

  // 我的点赞
  goMyLikes: function() {
    this.checkLoginAndNavigate('/pages/mine/likes/likes');
  },

  // 我的收藏
  goMyCollections: function() {
    this.checkLoginAndNavigate('/pages/mine/collections/collections');
  },

  // 浏览记录
  goViewHistory: function() {
    this.checkLoginAndNavigate('/pages/mine/history/history');
  },

  // 拨号记录
  goCallHistory: function() {
    this.checkLoginAndNavigate('/pages/mine/history/call');
  },

  // 会员服务
  goMembership: function() {
    this.checkLoginAndNavigate('/pages/mine/vip/vip');
  },

  // 联系客服
  contactService: function() {
    wx.makePhoneCall({
      phoneNumber: '400-xxx-xxxx',
      success: () => {
        console.log('拨打客服电话成功');
      },
      fail: (error) => {
        console.error('拨打客服电话失败:', error);
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },

  // 意见反馈
  goFeedback: function() {
    wx.navigateTo({
      url: '/pages/mine/feedback/feedback'
    });
  },

  // 页面跳转
  navigateTo(e) {
    console.log('navigateTo', e);
    const url = e.detail.url;
    console.log('navigateTo url', url);
    if (!this.data.hasUserInfo && url !== '/pages/login/login') {
      wx.navigateTo({
        url: '/pages/login/login'
      });
      return;
    }
    console.log('navigateTo url', url);
    wx.navigateTo({ url });
  },

  // 处理退出登录
  handleLogout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            userInfo: {},
            hasUserInfo: false,
            stats: {
              points: 0,
              favorites: 0,
              posts: 0,
              weeklyPosts: 0,
              exposureGrowth: 0,
              taskProgress: 0,
              remainingTasks: 0
            },
            posts: []
          });
          logout();
        }
      }
    });
  },

  // 获取用户统计数据
  getUserStats: async function() {
    if (!this.data.hasUserInfo) return;

    try {
      const res = await request({
        url: '/blade-chat/user/stats',
        method: 'GET'
      });

      if (res.code === 200) {
        this.setData({
          'stats.points': res.data.points || 2580,
          'stats.favorites': res.data.favorites || 46,
          'stats.posts': res.data.posts || 128,
          'stats.weeklyPosts': res.data.weeklyPosts || 5,
          'stats.exposureGrowth': res.data.exposureGrowth || 30,
          'stats.taskProgress': res.data.taskProgress || 60,
          'stats.remainingTasks': res.data.remainingTasks || 3
        });
      }
    } catch (err) {
      console.error('获取用户统计数据失败：', err);
    }
  },

  // 获取我的发布列表
  getMyPosts: async function() {
    if (!this.data.hasUserInfo) return;

    try {
      const res = await request({
        url: '/blade-chat/post/my',
        method: 'GET',
        data: {
          current: 1,
          size: 2
        }
      });
      if (res.code === 200) {
        //  处理图片
        const posts = res.data.records.map(post => {  
          return {
            ...post,
            images: post.images.split(',')
          }
        })
        console.log('获取我的发布列表 posts', posts);
        this.setData({
          posts: posts || this.data.posts
        });
      }
    } catch (err) {
      console.error('获取我的发布列表失败：', err);
    }
  },

  // 编辑帖子
  editPost(e) {
    const postId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/publish/publish?id=${postId}&type=edit`
    });
  },

  // 删除帖子
  deletePost(e) {
    // 数据源是元素的 data-xxx 比如 data-id="123"
    // const postId = e.currentTarget.dataset.id;
    // 数据源是子组件通过 triggerEvent 传递的数据对象
    const postId = e.detail.id;
    console.log(postId)
    wx.showModal({
      title: '提示',
      content: '确定要删除这条发布吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await request({
              url: `/blade-chat/post/${postId}`,
              method: 'DELETE'
            });
            
            if (result.code === 200) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              this.getMyPosts(); // 重新获取列表
            }
          } catch (err) {
            console.error('删除帖子失败：', err);
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  goMyHistory() {
    wx.navigateTo({
      url: '/pages/mine/history/history'
    })
  },

  // 跳转到我的发布列表
  goToMyPosts() {
    wx.navigateTo({
      url: '/pages/mine/my-posts/index'
    });
  },

  // 跳转到帖子详情
  goToPostDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pkg_common/pages/post/detail/detail?id=${id}`
    });
  }
}) 