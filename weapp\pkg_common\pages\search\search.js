const { getCategories, getPosts } = require('../../stores/indexStore.js');

Page({
  data: {
    searchText: '',
    categories: [],
    activeCategory: '全部',
    posts: [],
    loading: false,
    page: 1,
    pageSize: 10,
    hasMore: true
  },

  async onLoad() {
    await this.loadCategories();
    this.loadRecommendPosts();
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchText: e.detail.value
    });
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchText: ''
    });
  },

  // 取消搜索
  onCancel() {
    wx.navigateBack();
  },

  // 执行搜索
  async onSearch() {
    if (!this.data.searchText.trim()) {
      return;
    }
    this.setData({ page: 1, hasMore: true });
    await this.loadRecommendPosts(this.data.activeCategory, this.data.searchText);
  },

  // 点击AI助手
  onTapAI() {
    wx.switchTab({ url: '/pages/ai/ai' });
  },

  // 点击分类
  async onTapCategory(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      activeCategory: category,
      page: 1,
      hasMore: true
    });
    await this.loadRecommendPosts(category, this.data.searchText);
  },

  // 加载推荐帖子（支持分类和关键词）
  async loadRecommendPosts(category = '全部', keyword = '') {
    if (this.data.loading || !this.data.hasMore) return;
    this.setData({ loading: true });
    try {
      let params = {
        current: this.data.page,
        size: this.data.pageSize
      };
      if (category && category !== '全部') {
        params.categoryName = category;
      }
      if (keyword) {
        params.content = keyword;
      }
      const posts = await getPosts(params);
      this.setData({
        posts: this.data.page === 1 ? posts : [...this.data.posts, ...posts],
        hasMore: posts.length === this.data.pageSize
      });
    } catch (e) {
      wx.showToast({ title: '加载失败', icon: 'none' });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载分类
  async loadCategories() {
    const categories = await getCategories();
    this.setData({
      categories,
      activeCategory: categories[0] || '全部'
    });
  },

  // 下拉刷新
  async onPullDownRefresh() {
    this.setData({ page: 1, hasMore: true });
    await this.loadRecommendPosts(this.data.activeCategory, this.data.searchText);
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  async onReachBottom() {
    if (!this.data.hasMore || this.data.loading) return;
    this.setData({ page: this.data.page + 1 });
    await this.loadRecommendPosts(this.data.activeCategory, this.data.searchText);
  }
}); 