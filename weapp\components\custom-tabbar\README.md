# Custom TabBar 组件优化说明

## 优化内容

### 1. 🎨 视觉效果优化
- **激活状态显示**: 当前页面tab会高亮显示，使用不同颜色和图标
- **底部指示器**: 激活的tab底部会显示渐变色指示条
- **缩放动画**: 点击时有微妙的缩放反馈效果
- **发布按钮特效**: 
  - 渐变背景色
  - 呼吸灯动画效果
  - 点击时的波纹扩散效果
  - 激活时图标旋转45度

### 2. 🚀 交互体验优化
- **防抖处理**: 防止快速重复点击导致的多次跳转
- **切换动画**: 使用贝塞尔曲线实现流畅的过渡动画
- **状态管理**: 自动检测当前页面并更新激活状态
- **导航优化**: 根据页面类型自动选择`switchTab`或`navigateTo`

### 3. 🔧 功能增强
- **自动状态同步**: 页面切换时自动更新tabbar状态
- **登录检查**: 需要登录的页面会自动检查登录状态
- **错误处理**: 完善的错误处理和用户提示
- **事件回调**: 支持tab点击事件回调

## 使用方法

### 基本使用
```xml
<custom-tabbar show="{{showTabbar}}" bind:tabclick="onTabClick"></custom-tabbar>
```

### 属性说明
- `show`: 是否显示tabbar（Boolean，默认true）
- `bind:tabclick`: tab点击事件回调

### 事件回调
```javascript
onTabClick(e) {
  const { index, tabItem } = e.detail;
  console.log('点击了tab:', index, tabItem);
}
```

## 配置说明

### Tab配置
在组件的`data.tabList`中配置tab项：

```javascript
tabList: [
  {
    pagePath: '/pages/index/index',    // 页面路径
    text: '首页',                      // 显示文字
    iconPath: '/assets/images/tab/home.png',           // 默认图标
    selectedIconPath: '/assets/images/tab/home-active.png', // 激活图标
    isPublish: false                   // 是否为发布按钮
  }
]
```

### 样式定制
可以通过修改CSS变量来定制样式：

```css
.custom-tabbar {
  --tab-active-color: #ff6b6b;      /* 激活颜色 */
  --tab-normal-color: #999;         /* 默认颜色 */
  --tab-bg-color: #fff;             /* 背景颜色 */
}
```

## 动画效果

### 1. Tab切换动画
- 使用`cubic-bezier(0.25, 0.46, 0.45, 0.94)`缓动函数
- 激活时轻微放大(scale: 1.05)
- 点击时缩小反馈(scale: 0.95)

### 2. 发布按钮动画
- 呼吸灯效果：阴影明暗变化
- 点击波纹：从中心向外扩散的白色波纹
- 图标旋转：激活时旋转45度

### 3. 指示器动画
- 底部指示条从无到有的缩放动画
- 渐变色背景

## 性能优化

### 1. 防抖机制
- 导航过程中禁用所有点击事件
- 使用`isNavigating`状态控制

### 2. 状态同步
- 页面显示时自动更新当前激活状态
- 避免不必要的重复渲染

### 3. 动画优化
- 使用`transform`而非`left/top`属性
- 启用硬件加速
- 合理的动画时长和缓动函数

## 兼容性

- 支持微信小程序基础库 2.0+
- 兼容不同屏幕尺寸
- 响应式设计，小屏设备自动调整尺寸

## 注意事项

1. **页面路径配置**: 确保`tabList`中的`pagePath`与实际页面路径一致
2. **图标资源**: 确保激活和默认状态的图标文件存在
3. **登录检查**: 需要登录的页面会自动跳转到登录页面
4. **导航方式**: 组件会自动判断使用`switchTab`还是`navigateTo`

## 更新日志

### v2.0.0 (当前版本)
- ✨ 新增激活状态显示
- ✨ 新增底部指示器
- ✨ 新增防抖处理
- ✨ 新增发布按钮特效
- 🐛 修复页面切换方式问题
- 🎨 优化动画效果
- 📱 改进响应式适配
