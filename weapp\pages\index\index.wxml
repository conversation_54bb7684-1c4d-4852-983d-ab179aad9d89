<!-- 导航栏固定在顶部 -->
<custom-nav
  id="custom-nav"
  background="linear-gradient(to bottom, #ff6b6b, #ff8585)"
  text-color="#ffffff"
  show-location="{{true}}"
  show-back="{{false}}"
  fixed="{{true}}"
  bind:navReady="onNavReady"
  bind:showDrawer="onShowDrawer"
  bind:regionpickershow="onRegionPickerShow">
</custom-nav>

<!-- 吸顶分类栏（模拟吸顶） -->
<view wx:if="{{showStickyCategory}}" class="sticky-category-bar" style="top: {{navBarHeight}}px;">
  <scroll-view scroll-x class="category-tabs-scroll" show-scrollbar="false">
    <view class="category-tabs {{categories.length <= 1 ? 'single-tab' : ''}}">
      <view 
        class="tab-item {{selectedCategoryId === item.id ? 'active' : ''}}" 
        wx:for="{{categories}}" 
        wx:key="id" 
        data-index="{{index}}"
        bindtap="switchTab">
        {{item.name}}
      </view>
    </view>
  </scroll-view>
</view>

<!-- 内容区域 -->
<view wx:if="{{isRefreshing}}" class="refresh-coins-fixed" style="padding-top: {{navBarHeight}}px;">
  <block wx:for="{{coins}}" wx:key="index">
    <view
      class="coin"
      style="left: {{item.left}}vw; font-size: {{item.size}}rpx; animation-delay: {{item.delay}}s; --coin-rotate: {{item.rotate}}deg; --coin-swing: {{item.swing}}vw;"
    >🪙</view>
  </block>
</view>
<scroll-view
  scroll-y
  refresher-enabled="true"
  refresher-default-style="none"
  refresher-background="#FF7B7B"
  bindscroll="onScroll"
  bindrefresherrefresh="onPullDownRefresh"
  refresher-triggered="{{isRefreshing}}"
  class="scroll-container main-scroll-view"
  style="margin-top: {{navBarHeight}}px;height: calc(100vh - {{navBarHeight}}px);"
  scroll-into-view="{{scrollIntoView}}"
  enable-back-to-top="{{!reachedBottom}}"
  scroll-with-animation="{{true}}"
>
  <view class="main-content">
    <!-- 托盘和加载文字放在内容区顶部 -->
    <view wx:if="{{isRefreshing}}" class="refresh-bottom-area">
      <view class="coin-tray"></view>
      <text class="refresh-text">事事有着落，件件有回音...</text>
    </view>
    <!-- 数据统计区域 -->
    <stats-bar></stats-bar>
    <banner-swiper list="{{banners}}"></banner-swiper>
    <!-- 首页菜单 -->
    <quick-access list="{{quickAccess}}" bind:itemtap="onQuickAccessTap"></quick-access>
    
    <view class="card-section" id="category-section">
      <card>
        <view class="card-section-title" id="category-sticky">
          <view class="main-tabs">
            <view
              wx:for="{{['最新', '附近']}}"
              wx:key="index"
              class="tab-title-item {{currentTab === index ? 'active' : ''}}"
              data-index="{{index}}"
              bindtap="switchMainTab"
            >
              {{item}}
            </view>
          </view>
          <view class="map-btn" bindtap="onOpenMap">
            <text class="map-text">地图</text>
          </view>
        </view>
        <scroll-view scroll-x class="category-tabs-scroll" show-scrollbar="false" id="category-tabs-scroll">
          <view class="category-tabs {{categories.length <= 1 ? 'single-tab' : ''}}">
            <view 
              class="tab-item {{selectedCategoryId === item.id ? 'active' : ''}}" 
              wx:for="{{categories}}" 
              wx:key="id" 
              data-index="{{index}}"
              bindtap="switchTab">
              {{item.name}}
            </view>
          </view>
        </scroll-view>
        <view class="posts-container">
          <!-- 帖子列表 -->
          <block wx:for="{{posts}}" wx:key="id">
            <content-card post="{{item}}" bind:like="onLike" />
          </block>

          <!-- 底部状态区域 -->
          <view class="bottom-status">
            <!-- 预加载状态 -->
            <view wx:if="{{isPreloading}}" class="loading-more preloading">
              <text>智能预加载中...</text>
            </view>

            <!-- 加载中状态 -->
            <view wx:elif="{{loading}}" class="loading-more">
              <text>加载中...</text>
            </view>

            <!-- 已到达底部状态 -->
            <view bindtap="goToPublish" wx:elif="{{reachedBottom && posts.length > 0}}" class="reached-bottom">
              <text>🤔 没找到，不如发个帖子试试？</text>
            </view>

            <!-- 没有更多数据状态 -->
            <view bindtap="goToPublish" wx:elif="{{!hasMore && posts.length > 0}}" class="no-more">
              <text>🤔 没找到，不如发个帖子试试？</text>
            </view>


            <!-- 空数据状态 -->
            <view wx:elif="{{!loading && posts.length === 0}}" class="empty-state">
              <text>暂无数据</text>
            </view>
          </view>
        </view>
      </card>
    </view>
  </view>
</scroll-view>

<custom-tabbar show="{{showTabbar}}" />
