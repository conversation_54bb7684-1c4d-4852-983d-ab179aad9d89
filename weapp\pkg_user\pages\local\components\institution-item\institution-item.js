// pkg_user/pages/local/components/institution-item/institution-item.js
Component({
  properties: {
    institution: {
      type: Object,
      value: {}
    }
  },

  data: {
    imageList: []
  },

  lifetimes: {
    attached() {
      this.processInstitutionData();
    }
  },

  observers: {
    'institution': function(institution) {
      this.processInstitutionData();
    }
  },

  methods: {
    // 处理机构数据
    processInstitutionData() {
      const institution = this.data.institution;
      if (!institution) return;

      // 处理图片列表
      let imageList = [];
      if (institution.logo) {
        imageList.push(institution.logo);
      }
      if (institution.images && institution.images.length > 0) {
        imageList = imageList.concat(institution.images);
      }

      this.setData({
        imageList: imageList
      });
    },

    // 点击机构卡片
    onTap() {
      this.triggerEvent('tap', {
        institution: this.data.institution
      });
    },

    // 预览图片
    previewImage(e) {
      const current = e.currentTarget.dataset.current;
      wx.previewImage({
        current: current,
        urls: this.data.imageList
      });
    },

    // 点击更多按钮
    onTapMore(e) {
      e.stopPropagation();
      const institution = this.data.institution;
      
      wx.showActionSheet({
        itemList: ['拨打电话', '查看位置', '收藏机构'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              if (institution.phone) {
                wx.makePhoneCall({
                  phoneNumber: institution.phone
                });
              }
              break;
            case 1:
              if (institution.latitude && institution.longitude) {
                wx.openLocation({
                  latitude: parseFloat(institution.latitude),
                  longitude: parseFloat(institution.longitude),
                  name: institution.name,
                  address: institution.address
                });
              }
              break;
            case 2:
              this.toggleFavorite();
              break;
          }
        }
      });
    },

    // 切换收藏状态
    toggleFavorite() {
      // 这里可以调用收藏API
      wx.showToast({
        title: '收藏功能开发中',
        icon: 'none'
      });
    }
  }
});
