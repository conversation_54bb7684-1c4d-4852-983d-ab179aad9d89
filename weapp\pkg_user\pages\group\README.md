# 群组页面接口接入说明

## 功能概述

本次更新为群组页面接入了后端接口，实现了以下功能：

1. **分类管理**：从后端获取群分类列表
2. **群组查询**：根据选择的分类查询对应的群组信息
3. **搜索功能**：支持按关键词搜索群组
4. **分页加载**：支持上拉加载更多和下拉刷新

## 文件结构

```
weapp/pkg_user/pages/group/
├── index.js          # 页面逻辑文件（已更新）
├── index.wxml        # 页面模板文件（已更新）
├── index.wxss        # 页面样式文件（已更新）
├── index.json        # 页面配置文件（已更新）
├── services/
│   └── groupApi.js   # API服务模块（新增）
└── README.md         # 说明文档（本文件）
```

## 自定义导航栏

页面使用了自定义导航栏组件 `custom-nav`，具有以下特性：

### 导航栏配置
- **标题**：群列表
- **返回按钮**：显示（可返回上一页）
- **搜索功能**：隐藏（使用页面内搜索）
- **位置选择**：隐藏
- **背景色**：#FF7D7D（红色主题）
- **文字色**：白色

### 滚动效果
- 页面滚动时导航栏会有渐变效果
- 滚动超过阈值时背景变为白色，文字变为黑色
- 具有平滑的过渡动画

## 主要功能

### 1. 分类列表加载
- 页面加载时自动获取后端分类数据
- 自动添加"全部"分类选项
- 支持分类点击切换

### 2. 群组数据加载
- 根据选中分类筛选群组
- 支持分页加载（每页20条）
- 支持上拉加载更多

### 3. 搜索功能
- 支持按群名称搜索
- 可结合分类进行筛选搜索

### 4. 用户体验优化
- 加载状态提示
- 空数据状态显示
- 错误处理和友好提示
- 下拉刷新功能

## API接口说明

### 获取分类列表
- **接口**：`GET /balde-ad/groupcategory/list`
- **返回**：分类列表数据

### 获取群组列表
- **接口**：`GET /blade-ad/groupinfo/list`
- **参数**：
  - `current`: 当前页码
  - `size`: 每页大小
  - `groupType`: 群组类型（可选）
  - `groupName`: 群名称（搜索时使用）

## 数据格式

### 分类数据格式
```javascript
{
  id: 1,
  categoryName: "同城交友",
  categoryImage: "/assets/icons/discover.png",
  sortOrder: 1
}
```

### 群组数据格式
```javascript
{
  id: 1,
  groupName: "滑县便民信息",
  groupDesc: "便民信息进群",
  groupImage: "/assets/icons/local.png",
  groupWeixin: "微信群二维码链接",
  groupType: "同城交友"
}
```

## 使用说明

1. **页面加载**：页面会自动加载分类和群组数据
2. **分类切换**：点击分类项可切换显示对应群组
3. **搜索功能**：在搜索框输入关键词后点击搜索按钮
4. **加载更多**：滚动到页面底部自动加载更多数据
5. **刷新数据**：下拉页面可刷新数据

## 注意事项

1. 确保后端接口正常运行
2. 检查网络请求配置是否正确
3. 注意处理接口返回的错误情况
4. 建议在真机上测试网络请求功能
5. 自定义导航栏需要在真机上测试适配效果

## 测试要点

### 导航栏测试
1. **基本显示**：检查导航栏是否正常显示标题和返回按钮
2. **滚动效果**：向下滚动页面，观察导航栏背景色和文字色的变化
3. **返回功能**：点击返回按钮是否能正常返回上一页
4. **适配测试**：在不同机型上测试导航栏高度适配

### 功能测试
1. **分类切换**：点击不同分类，检查群组列表是否正确筛选
2. **搜索功能**：输入关键词搜索，验证搜索结果
3. **分页加载**：滚动到底部，检查是否正确加载更多数据
4. **下拉刷新**：下拉页面，验证数据刷新功能

## 样式优化说明

### 分类列表布局
- **排列方式**：从左开始排序，一行最多显示5个分类
- **高亮效果**：选中状态使用反转高亮色（白色文字 + 红色背景）
- **视觉效果**：
  - 选中时图标有红色圆形背景和阴影
  - 选中时文字有红色背景和圆角边框
  - 整体有轻微的缩放动画效果

### CSS 关键样式
```css
/* 分类列表容器 - 从左开始排列 */
.group-category-list {
  justify-content: flex-start;
}

/* 分类项 - 每行5个 */
.category-item {
  width: 20%;
}

/* 选中状态 - 反转高亮色 */
.category-item.active .category-label {
  color: #ffffff;
  background: #FF7D7D;
  border-radius: 20rpx;
}
```

## 后续优化建议

1. 添加缓存机制提升加载速度
2. 优化图片加载和显示
3. 添加更多筛选条件
4. 支持群组收藏功能
