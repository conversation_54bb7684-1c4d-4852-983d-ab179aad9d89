<!-- pkg_user/pages/add-card/add-card.wxml -->
<view class="add-card-container">
  <!-- 顶部工具栏 -->
  <view class="toolbar">
    <view class="tool-item" bindtap="onScanCard">
      <text class="tool-icon">📷</text>
      <text class="tool-text">扫描名片</text>
    </view>
    <view class="tool-item" bindtap="onPreview">
      <text class="tool-icon">👁</text>
      <text class="tool-text">预览</text>
    </view>
  </view>

  <scroll-view scroll-y class="form-scroll">
    <!-- 头像选择 -->
    <view class="avatar-section">
      <text class="section-title">头像</text>
      <view class="avatar-picker" bindtap="onChooseAvatar">
        <image wx:if="{{formData.avatar}}" src="{{formData.avatar}}" class="avatar-preview" mode="aspectFill" />
        <view wx:else class="avatar-placeholder">
          <text class="avatar-add-icon">+</text>
          <text class="avatar-add-text">添加头像</text>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="form-section">
      <text class="section-title">基本信息</text>
      
      <view class="form-item">
        <text class="form-label">姓名 *</text>
        <input 
          class="form-input" 
          placeholder="请输入姓名"
          value="{{formData.name}}"
          data-field="name"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <text class="form-label">公司</text>
        <input 
          class="form-input" 
          placeholder="请输入公司名称"
          value="{{formData.company}}"
          data-field="company"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <text class="form-label">职位</text>
        <input 
          class="form-input" 
          placeholder="请输入职位"
          value="{{formData.position}}"
          data-field="position"
          bindinput="onInputChange"
        />
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="form-section">
      <text class="section-title">联系方式</text>
      
      <view class="form-item">
        <text class="form-label">手机号 *</text>
        <input 
          class="form-input" 
          placeholder="请输入手机号"
          type="number"
          value="{{formData.phone}}"
          data-field="phone"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <text class="form-label">邮箱</text>
        <input 
          class="form-input" 
          placeholder="请输入邮箱地址"
          value="{{formData.email}}"
          data-field="email"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <text class="form-label">微信号</text>
        <input 
          class="form-input" 
          placeholder="请输入微信号"
          value="{{formData.wechat}}"
          data-field="wechat"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <text class="form-label">网站</text>
        <input 
          class="form-input" 
          placeholder="请输入网站地址"
          value="{{formData.website}}"
          data-field="website"
          bindinput="onInputChange"
        />
      </view>
    </view>

    <!-- 其他信息 -->
    <view class="form-section">
      <text class="section-title">其他信息</text>
      
      <view class="form-item">
        <text class="form-label">地址</text>
        <input 
          class="form-input" 
          placeholder="请输入地址"
          value="{{formData.address}}"
          data-field="address"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <text class="form-label">分类</text>
        <view class="category-selector">
          <view 
            class="category-option {{formData.category === item.id ? 'active' : ''}}"
            wx:for="{{categories}}" 
            wx:key="id"
            data-id="{{item.id}}"
            bindtap="onCategoryChange">
            {{item.name}}
          </view>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">备注</text>
        <textarea 
          class="form-textarea" 
          placeholder="请输入备注信息"
          value="{{formData.remark}}"
          data-field="remark"
          bindinput="onInputChange"
          maxlength="200"
        />
      </view>
    </view>
  </scroll-view>

  <!-- 底部按钮 -->
  <view class="submit-section">
    <button 
      class="submit-btn {{submitting ? 'submitting' : ''}}" 
      bindtap="onSubmit"
      disabled="{{submitting}}"
    >
      {{submitting ? '保存中...' : '保存名片'}}
    </button>
  </view>
</view>
