<!-- 名片编辑页面 -->
<view class="add-card-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 表单内容 -->
  <view wx:else class="form-content">
    <!-- 顶部工具栏 -->
    <view class="toolbar">
      <view class="tool-item" bindtap="onPreview">
        <text class="tool-icon">👁️</text>
        <text class="tool-text">预览</text>
      </view>
      <view class="tool-item" bindtap="onReset">
        <text class="tool-icon">🔄</text>
        <text class="tool-text">重置</text>
      </view>
    </view>

    <scroll-view scroll-y class="form-scroll">
      <!-- 头像选择 -->
      <view class="form-section">
        <text class="section-title">头像设置</text>
        <view class="avatar-picker" bindtap="onChooseAvatar">
          <image wx:if="{{formData.avatar}}" src="{{formData.avatar}}" class="avatar-preview" mode="aspectFill" />
          <view wx:else class="avatar-placeholder">
            <text class="upload-icon">📷</text>
            <text class="avatar-add-text">添加头像</text>
          </view>
        </view>
      </view>

      <!-- 基本信息 -->
      <view class="form-section">
        <text class="section-title">基本信息</text>

        <view class="form-item">
          <text class="form-label required">姓名</text>
          <input
            class="form-input"
            placeholder="请输入真实姓名"
            value="{{formData.fullName}}"
            data-field="fullName"
            bindinput="onInputChange"
            maxlength="20"
          />
        </view>

        <view class="form-item">
          <text class="form-label">性别</text>
          <picker
            class="form-picker"
            mode="selector"
            range="{{genderOptions}}"
            range-key="label"
            value="{{formData.gender}}"
            bindchange="onGenderChange"
          >
            <view class="picker-display">
              {{genderOptions[formData.gender].label}}
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>

        <view class="form-item">
          <text class="form-label">公司</text>
          <input
            class="form-input"
            placeholder="请输入公司名称"
            value="{{formData.company}}"
            data-field="company"
            bindinput="onInputChange"
            maxlength="50"
        />
      </view>

      <view class="form-item">
        <text class="form-label">职位</text>
        <input
          class="form-input"
          placeholder="请输入职位"
          value="{{formData.jobTitle}}"
          data-field="jobTitle"
          bindinput="onInputChange"
          maxlength="30"
        />
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="form-section">
      <text class="section-title">联系方式</text>
      
      <view class="form-item">
        <text class="form-label">手机号 *</text>
        <input 
          class="form-input" 
          placeholder="请输入手机号"
          type="number"
          value="{{formData.phone}}"
          data-field="phone"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <text class="form-label">邮箱</text>
        <input 
          class="form-input" 
          placeholder="请输入邮箱地址"
          value="{{formData.email}}"
          data-field="email"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <text class="form-label">微信号</text>
        <input
          class="form-input"
          placeholder="请输入微信号"
          value="{{formData.weixin}}"
          data-field="weixin"
          bindinput="onInputChange"
          maxlength="30"
        />
      </view>

      <view class="form-item">
        <text class="form-label">网站</text>
        <input
          class="form-input"
          placeholder="请输入网站地址"
          value="{{formData.website}}"
          data-field="website"
          bindinput="onInputChange"
          maxlength="100"
        />
      </view>

      <view class="form-item">
        <text class="form-label">地址</text>
        <input
          class="form-input"
          placeholder="请输入联系地址"
          value="{{formData.address}}"
          data-field="address"
          bindinput="onInputChange"
          maxlength="100"
        />
      </view>
    </view>

    <!-- 业务信息 -->
    <view class="form-section">
      <text class="section-title">业务信息</text>

      <view class="form-item">
        <text class="form-label">业务简介</text>
        <textarea
          class="form-textarea"
          placeholder="请简要介绍您的业务或专长"
          value="{{formData.businessProfile}}"
          data-field="businessProfile"
          bindinput="onInputChange"
          maxlength="200"
          show-confirm-bar="{{false}}"
        />
      </view>



      <view class="form-item">
        <text class="form-label">备注信息</text>
        <textarea
          class="form-textarea"
          placeholder="其他补充信息（选填）"
          value="{{formData.description}}"
          data-field="description"
          bindinput="onInputChange"
          maxlength="300"
          show-confirm-bar="{{false}}"
        />
      </view>
    </view>

    <!-- 隐私设置 -->
    <view class="form-section">
      <text class="section-title">隐私设置</text>

      <view class="form-item">
        <text class="form-label">公开状态</text>
        <picker
          class="form-picker"
          mode="selector"
          range="{{publicOptions}}"
          range-key="label"
          value="{{formData.isPublic}}"
          bindchange="onPublicChange"
        >
          <view class="picker-display">
            {{publicOptions[formData.isPublic].label}}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>

      <text class="privacy-tip">
        公开：其他用户可以搜索和查看您的名片<br/>
        私密：只有您分享给别人才能查看
      </text>
    </view>
  </scroll-view>

  <!-- 底部按钮 -->
  <view class="bottom-actions">
    <button
      class="action-btn preview-btn"
      bindtap="onPreview"
    >
      <text class="btn-icon">👁️</text>
      预览名片
    </button>
    <button
      class="action-btn submit-btn {{submitting ? 'loading' : ''}}"
      bindtap="onSubmit"
      disabled="{{submitting}}"
    >
      <text wx:if="{{!submitting}}" class="btn-icon">💾</text>
      {{submitting ? '保存中...' : (mode === 'edit' ? '更新名片' : '保存名片')}}
    </button>
  </view>
  </view>
</view>
