# SpringBlade 接口文档系统


**简介**:SpringBlade 接口文档系统


**HOST**:https://wechat.langchuanxinxi.cn


**联系人**:smallchill


**Version**:4.4.0


**接口路径**:/v3/api-docs/微信小程序


[TOC]






# 小程序-消息模块


## 标记消息为已读


**接口地址**:`/blade-chat/message/{id}/read`


**请求方式**:`PUT`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>根据消息ID将消息状态设为已读</p>



**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|消息ID|path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RObject|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 获取所有消息模板


**接口地址**:`/blade-chat/message/templates`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>返回所有可用的消息模板列表</p>



**请求参数**:


暂无


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RListMessageTemplateVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|array|MessageTemplateVO|
|&emsp;&emsp;id|主键ID|integer(int64)||
|&emsp;&emsp;typeCode|消息类型编码|string||
|&emsp;&emsp;typeName|消息类型名称|string||
|&emsp;&emsp;titleTemplate|消息标题模板|string||
|&emsp;&emsp;contentTemplate|消息内容模板|string||
|&emsp;&emsp;isSystem|是否为系统内置模板|boolean||
|&emsp;&emsp;status|模板状态(1:启用 0:禁用)|integer(int32)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": [
		{
			"id": 0,
			"typeCode": "",
			"typeName": "",
			"titleTemplate": "",
			"contentTemplate": "",
			"isSystem": true,
			"status": 0
		}
	],
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 新增消息模板


**接口地址**:`/blade-chat/message/templates`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>创建一个新的消息模板</p>



**请求示例**:


```javascript
{
  "typeCode": "",
  "typeName": "",
  "titleTemplate": "",
  "contentTemplate": ""
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|messageTemplateCreateDTO|新增消息模板DTO|body|true|MessageTemplateCreateDTO|MessageTemplateCreateDTO|
|&emsp;&emsp;typeCode|消息类型编码||true|string||
|&emsp;&emsp;typeName|消息类型名称||true|string||
|&emsp;&emsp;titleTemplate|消息标题模板||true|string||
|&emsp;&emsp;contentTemplate|消息内容模板||true|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RObject|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 更新消息模板


**接口地址**:`/blade-chat/message/templates`


**请求方式**:`PUT`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>根据ID更新消息模板内容和状态</p>



**请求示例**:


```javascript
{
  "id": 0,
  "titleTemplate": "",
  "contentTemplate": "",
  "status": 0
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|messageTemplateUpdateDTO|模板更新DTO|body|true|MessageTemplateUpdateDTO|MessageTemplateUpdateDTO|
|&emsp;&emsp;id|模板ID||true|integer(int64)||
|&emsp;&emsp;titleTemplate|消息标题模板||false|string||
|&emsp;&emsp;contentTemplate|消息内容模板||false|string||
|&emsp;&emsp;status|模板状态(1:启用 0:禁用)||false|integer(int32)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RObject|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 分页查询消息列表


**接口地址**:`/blade-chat/message`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>支持标题、内容、类型、已读状态、时间范围等多条件分页查询</p>



**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|userId|用户id|query|false|string||
|title|消息标题|query|false|string||
|content|消息内容|query|false|string||
|messageType|消息类型|query|false|string||
|isRead|是否已读|query|false|string||
|startTime|开始时间（格式：yyyy-MM-dd HH:mm:ss）|query|false|string||
|endTime|结束时间（格式：yyyy-MM-dd HH:mm:ss）|query|false|string||
|current|当前页|query|false|string||
|size|每页的数量|query|false|string||
|ascs||query|false|string||
|descs||query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RIPageMessageVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||IPageMessageVO|IPageMessageVO|
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;records||array|MessageVO|
|&emsp;&emsp;&emsp;&emsp;id|消息ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;title|消息标题|string||
|&emsp;&emsp;&emsp;&emsp;content|消息内容|string||
|&emsp;&emsp;&emsp;&emsp;messageType|消息类型|string||
|&emsp;&emsp;&emsp;&emsp;isRead|是否已读|boolean||
|&emsp;&emsp;&emsp;&emsp;relatedId|关联业务ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;createTime|创建时间|string(date-time)||
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"size": 0,
		"records": [
			{
				"id": 0,
				"title": "",
				"content": "",
				"messageType": "",
				"isRead": true,
				"relatedId": 0,
				"createTime": ""
			}
		],
		"current": 0,
		"total": 0,
		"pages": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 批量删除消息


**接口地址**:`/blade-chat/message`


**请求方式**:`DELETE`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>根据ID列表批量删除消息</p>



**请求示例**:


```javascript
[]
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|integers|integer|body|true|array||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RObject|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


# 小程序发布


## 更新帖子


**接口地址**:`/miniapp/post/{id}`


**请求方式**:`PUT`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>更新指定帖子</p>



**请求示例**:


```javascript
{
  "title": "",
  "content": "",
  "images": [],
  "address": "",
  "geoLocation": "",
  "tags": [],
  "contactName": "",
  "contactPhone": "",
  "categoryId": 0,
  "openId": "",
  "nickName": "",
  "avatarUrl": ""
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|帖子ID|path|true|integer(int64)||
|postPublishDTO|小程序发布帖子DTO|body|true|PostPublishDTO|PostPublishDTO|
|&emsp;&emsp;title|标题||true|string||
|&emsp;&emsp;content|内容||true|string||
|&emsp;&emsp;images|图片列表||false|array|string|
|&emsp;&emsp;address|发布地址||false|string||
|&emsp;&emsp;geoLocation|地理位置||false|string||
|&emsp;&emsp;tags|标签列表||false|array|string|
|&emsp;&emsp;contactName|联系人姓名||false|string||
|&emsp;&emsp;contactPhone|联系电话||false|string||
|&emsp;&emsp;categoryId|分类ID||true|integer(int64)||
|&emsp;&emsp;openId|小程序用户OpenID||true|string||
|&emsp;&emsp;nickName|用户昵称||false|string||
|&emsp;&emsp;avatarUrl|用户头像||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RSupPost|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||SupPost|SupPost|
|&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;location|地址|string||
|&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;title|标题|string||
|&emsp;&emsp;content|内容|string||
|&emsp;&emsp;images|图片|string||
|&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;contactName||string||
|&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;viewCount|浏览数|integer(int32)||
|&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;userInfo||object||
|&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;avatar||string||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"id": 0,
		"location": "",
		"longitude": 0,
		"latitude": 0,
		"contactType": "",
		"contactNumber": "",
		"title": "",
		"content": "",
		"images": "",
		"address": "",
		"publishTime": "",
		"publishStatus": "",
		"timeStatus": "",
		"auditStatus": "",
		"geoLocation": "",
		"tags": [],
		"contactName": "",
		"contactPhone": "",
		"top": "",
		"completed": 0,
		"categoryId": 0,
		"likeCount": 0,
		"favoriteCount": 0,
		"viewCount": 0,
		"auditRemark": "",
		"auditTime": "",
		"auditUserId": 0,
		"category": {
			"id": 0,
			"name": "",
			"parentId": 0,
			"icon": "",
			"description": "",
			"sort": 0,
			"enabled": 0,
			"enableAudit": 0,
			"tip": "",
			"maxImages": 0,
			"allowTags": "",
			"tags": [
				{
					"id": 0,
					"tagName": "",
					"categoryId": 0,
					"color": "",
					"icon": "",
					"sort": 0,
					"enabled": 0,
					"useCount": 0,
					"isSystem": 0,
					"description": "",
					"sortOrder": ""
				}
			],
			"children": [
				{}
			],
			"postCount": 0,
			"parentName": ""
		},
		"userInfo": {},
		"tagList": [
			{
				"id": 0,
				"tagName": "",
				"categoryId": 0,
				"color": "",
				"icon": "",
				"sort": 0,
				"enabled": 0,
				"useCount": 0,
				"isSystem": 0,
				"description": "",
				"sortOrder": ""
			}
		],
		"avatar": ""
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 删除帖子


**接口地址**:`/miniapp/post/{id}`


**请求方式**:`DELETE`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>删除指定帖子</p>



**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|帖子ID|path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 点赞帖子


**接口地址**:`/miniapp/post/{id}/like`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>对指定帖子进行点赞</p>



**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|帖子ID|path|true|integer(int64)||
|openId|用户OpenID|query|true|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 收藏帖子


**接口地址**:`/miniapp/post/{id}/favorite`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>收藏指定帖子</p>



**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|帖子ID|path|true|integer(int64)||
|openId|用户OpenID|query|true|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 发布帖子


**接口地址**:`/miniapp/post/publish`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>小程序用户发布帖子</p>



**请求示例**:


```javascript
{
  "title": "",
  "content": "",
  "images": [],
  "address": "",
  "geoLocation": "",
  "tags": [],
  "contactName": "",
  "contactPhone": "",
  "categoryId": 0,
  "openId": "",
  "nickName": "",
  "avatarUrl": ""
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|postPublishDTO|小程序发布帖子DTO|body|true|PostPublishDTO|PostPublishDTO|
|&emsp;&emsp;title|标题||true|string||
|&emsp;&emsp;content|内容||true|string||
|&emsp;&emsp;images|图片列表||false|array|string|
|&emsp;&emsp;address|发布地址||false|string||
|&emsp;&emsp;geoLocation|地理位置||false|string||
|&emsp;&emsp;tags|标签列表||false|array|string|
|&emsp;&emsp;contactName|联系人姓名||false|string||
|&emsp;&emsp;contactPhone|联系电话||false|string||
|&emsp;&emsp;categoryId|分类ID||true|integer(int64)||
|&emsp;&emsp;openId|小程序用户OpenID||true|string||
|&emsp;&emsp;nickName|用户昵称||false|string||
|&emsp;&emsp;avatarUrl|用户头像||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RSupPost|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||SupPost|SupPost|
|&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;location|地址|string||
|&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;title|标题|string||
|&emsp;&emsp;content|内容|string||
|&emsp;&emsp;images|图片|string||
|&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;contactName||string||
|&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;viewCount|浏览数|integer(int32)||
|&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;userInfo||object||
|&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;avatar||string||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"id": 0,
		"location": "",
		"longitude": 0,
		"latitude": 0,
		"contactType": "",
		"contactNumber": "",
		"title": "",
		"content": "",
		"images": "",
		"address": "",
		"publishTime": "",
		"publishStatus": "",
		"timeStatus": "",
		"auditStatus": "",
		"geoLocation": "",
		"tags": [],
		"contactName": "",
		"contactPhone": "",
		"top": "",
		"completed": 0,
		"categoryId": 0,
		"likeCount": 0,
		"favoriteCount": 0,
		"viewCount": 0,
		"auditRemark": "",
		"auditTime": "",
		"auditUserId": 0,
		"category": {
			"id": 0,
			"name": "",
			"parentId": 0,
			"icon": "",
			"description": "",
			"sort": 0,
			"enabled": 0,
			"enableAudit": 0,
			"tip": "",
			"maxImages": 0,
			"allowTags": "",
			"tags": [
				{
					"id": 0,
					"tagName": "",
					"categoryId": 0,
					"color": "",
					"icon": "",
					"sort": 0,
					"enabled": 0,
					"useCount": 0,
					"isSystem": 0,
					"description": "",
					"sortOrder": ""
				}
			],
			"children": [
				{}
			],
			"postCount": 0,
			"parentName": ""
		},
		"userInfo": {},
		"tagList": [
			{
				"id": 0,
				"tagName": "",
				"categoryId": 0,
				"color": "",
				"icon": "",
				"sort": 0,
				"enabled": 0,
				"useCount": 0,
				"isSystem": 0,
				"description": "",
				"sortOrder": ""
			}
		],
		"avatar": ""
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 新增帖子


**接口地址**:`/miniapp/post/create`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>小程序用户新增帖子</p>



**请求示例**:


```javascript
{
  "title": "",
  "content": "",
  "images": [],
  "address": "",
  "geoLocation": "",
  "tags": [],
  "contactName": "",
  "contactPhone": "",
  "categoryId": 0,
  "openId": "",
  "nickName": "",
  "avatarUrl": ""
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|postPublishDTO|小程序发布帖子DTO|body|true|PostPublishDTO|PostPublishDTO|
|&emsp;&emsp;title|标题||true|string||
|&emsp;&emsp;content|内容||true|string||
|&emsp;&emsp;images|图片列表||false|array|string|
|&emsp;&emsp;address|发布地址||false|string||
|&emsp;&emsp;geoLocation|地理位置||false|string||
|&emsp;&emsp;tags|标签列表||false|array|string|
|&emsp;&emsp;contactName|联系人姓名||false|string||
|&emsp;&emsp;contactPhone|联系电话||false|string||
|&emsp;&emsp;categoryId|分类ID||true|integer(int64)||
|&emsp;&emsp;openId|小程序用户OpenID||true|string||
|&emsp;&emsp;nickName|用户昵称||false|string||
|&emsp;&emsp;avatarUrl|用户头像||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RSupPost|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||SupPost|SupPost|
|&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;location|地址|string||
|&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;title|标题|string||
|&emsp;&emsp;content|内容|string||
|&emsp;&emsp;images|图片|string||
|&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;contactName||string||
|&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;viewCount|浏览数|integer(int32)||
|&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;userInfo||object||
|&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;avatar||string||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"id": 0,
		"location": "",
		"longitude": 0,
		"latitude": 0,
		"contactType": "",
		"contactNumber": "",
		"title": "",
		"content": "",
		"images": "",
		"address": "",
		"publishTime": "",
		"publishStatus": "",
		"timeStatus": "",
		"auditStatus": "",
		"geoLocation": "",
		"tags": [],
		"contactName": "",
		"contactPhone": "",
		"top": "",
		"completed": 0,
		"categoryId": 0,
		"likeCount": 0,
		"favoriteCount": 0,
		"viewCount": 0,
		"auditRemark": "",
		"auditTime": "",
		"auditUserId": 0,
		"category": {
			"id": 0,
			"name": "",
			"parentId": 0,
			"icon": "",
			"description": "",
			"sort": 0,
			"enabled": 0,
			"enableAudit": 0,
			"tip": "",
			"maxImages": 0,
			"allowTags": "",
			"tags": [
				{
					"id": 0,
					"tagName": "",
					"categoryId": 0,
					"color": "",
					"icon": "",
					"sort": 0,
					"enabled": 0,
					"useCount": 0,
					"isSystem": 0,
					"description": "",
					"sortOrder": ""
				}
			],
			"children": [
				{}
			],
			"postCount": 0,
			"parentName": ""
		},
		"userInfo": {},
		"tagList": [
			{
				"id": 0,
				"tagName": "",
				"categoryId": 0,
				"color": "",
				"icon": "",
				"sort": 0,
				"enabled": 0,
				"useCount": 0,
				"isSystem": 0,
				"description": "",
				"sortOrder": ""
			}
		],
		"avatar": ""
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 根据分类获取标签


**接口地址**:`/miniapp/post/tags/{categoryId}`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>根据分类ID获取该分类下的标签</p>



**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|categoryId|分类ID|path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RListTag|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|array|Tag|
|&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;description||string||
|&emsp;&emsp;sortOrder||string||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": [
		{
			"id": 0,
			"tagName": "",
			"categoryId": 0,
			"color": "",
			"icon": "",
			"sort": 0,
			"enabled": 0,
			"useCount": 0,
			"isSystem": 0,
			"description": "",
			"sortOrder": ""
		}
	],
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 获取我的帖子


**接口地址**:`/miniapp/post/my-posts`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>获取当前用户的帖子列表</p>



**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|current|页码|query|false|integer(int32)||
|size|每页大小|query|false|integer(int32)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RIPageSupPostVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||IPageSupPostVO|IPageSupPostVO|
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;records||array|SupPostVO|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;location|地址|string||
|&emsp;&emsp;&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;&emsp;&emsp;title|标题|string||
|&emsp;&emsp;&emsp;&emsp;content|内容|string||
|&emsp;&emsp;&emsp;&emsp;images|图片|string||
|&emsp;&emsp;&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;&emsp;&emsp;contactName||string||
|&emsp;&emsp;&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryId||integer(int64)||
|&emsp;&emsp;&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;viewCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;&emsp;&emsp;userInfo||object||
|&emsp;&emsp;&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;avatar||string||
|&emsp;&emsp;&emsp;&emsp;isLiked||boolean||
|&emsp;&emsp;&emsp;&emsp;isFavorite||boolean||
|&emsp;&emsp;&emsp;&emsp;nickname||string||
|&emsp;&emsp;&emsp;&emsp;feedbackCount|反馈数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryName||string||
|&emsp;&emsp;&emsp;&emsp;viewTime||string(date-time)||
|&emsp;&emsp;&emsp;&emsp;viewId||integer(int64)||
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"size": 0,
		"records": [
			{
				"id": 0,
				"location": "",
				"longitude": 0,
				"latitude": 0,
				"contactType": "",
				"contactNumber": "",
				"title": "",
				"content": "",
				"images": "",
				"address": "",
				"publishTime": "",
				"publishStatus": "",
				"timeStatus": "",
				"auditStatus": "",
				"geoLocation": "",
				"tags": [],
				"contactName": "",
				"contactPhone": "",
				"top": "",
				"completed": 0,
				"categoryId": 0,
				"likeCount": 0,
				"favoriteCount": 0,
				"viewCount": 0,
				"auditRemark": "",
				"auditTime": "",
				"auditUserId": 0,
				"category": {
					"id": 0,
					"name": "",
					"parentId": 0,
					"icon": "",
					"description": "",
					"sort": 0,
					"enabled": 0,
					"enableAudit": 0,
					"tip": "",
					"maxImages": 0,
					"allowTags": "",
					"tags": [
						{
							"id": 0,
							"tagName": "",
							"categoryId": 0,
							"color": "",
							"icon": "",
							"sort": 0,
							"enabled": 0,
							"useCount": 0,
							"isSystem": 0,
							"description": "",
							"sortOrder": ""
						}
					],
					"children": [
						{}
					],
					"postCount": 0,
					"parentName": ""
				},
				"userInfo": {},
				"tagList": [
					{
						"id": 0,
						"tagName": "",
						"categoryId": 0,
						"color": "",
						"icon": "",
						"sort": 0,
						"enabled": 0,
						"useCount": 0,
						"isSystem": 0,
						"description": "",
						"sortOrder": ""
					}
				],
				"avatar": "",
				"isLiked": true,
				"isFavorite": true,
				"nickname": "",
				"feedbackCount": 0,
				"categoryName": "",
				"viewTime": "",
				"viewId": 0
			}
		],
		"current": 0,
		"total": 0,
		"pages": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 获取帖子列表


**接口地址**:`/miniapp/post/list`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>分页获取帖子列表</p>



**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|categoryId|分类ID|query|false|integer(int64)||
|keyword|关键词|query|false|string||
|current|页码|query|false|integer(int32)||
|size|每页大小|query|false|integer(int32)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RIPageSupPostVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||IPageSupPostVO|IPageSupPostVO|
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;records||array|SupPostVO|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;location|地址|string||
|&emsp;&emsp;&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;&emsp;&emsp;title|标题|string||
|&emsp;&emsp;&emsp;&emsp;content|内容|string||
|&emsp;&emsp;&emsp;&emsp;images|图片|string||
|&emsp;&emsp;&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;&emsp;&emsp;contactName||string||
|&emsp;&emsp;&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryId||integer(int64)||
|&emsp;&emsp;&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;viewCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;&emsp;&emsp;userInfo||object||
|&emsp;&emsp;&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;avatar||string||
|&emsp;&emsp;&emsp;&emsp;isLiked||boolean||
|&emsp;&emsp;&emsp;&emsp;isFavorite||boolean||
|&emsp;&emsp;&emsp;&emsp;nickname||string||
|&emsp;&emsp;&emsp;&emsp;feedbackCount|反馈数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryName||string||
|&emsp;&emsp;&emsp;&emsp;viewTime||string(date-time)||
|&emsp;&emsp;&emsp;&emsp;viewId||integer(int64)||
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"size": 0,
		"records": [
			{
				"id": 0,
				"location": "",
				"longitude": 0,
				"latitude": 0,
				"contactType": "",
				"contactNumber": "",
				"title": "",
				"content": "",
				"images": "",
				"address": "",
				"publishTime": "",
				"publishStatus": "",
				"timeStatus": "",
				"auditStatus": "",
				"geoLocation": "",
				"tags": [],
				"contactName": "",
				"contactPhone": "",
				"top": "",
				"completed": 0,
				"categoryId": 0,
				"likeCount": 0,
				"favoriteCount": 0,
				"viewCount": 0,
				"auditRemark": "",
				"auditTime": "",
				"auditUserId": 0,
				"category": {
					"id": 0,
					"name": "",
					"parentId": 0,
					"icon": "",
					"description": "",
					"sort": 0,
					"enabled": 0,
					"enableAudit": 0,
					"tip": "",
					"maxImages": 0,
					"allowTags": "",
					"tags": [
						{
							"id": 0,
							"tagName": "",
							"categoryId": 0,
							"color": "",
							"icon": "",
							"sort": 0,
							"enabled": 0,
							"useCount": 0,
							"isSystem": 0,
							"description": "",
							"sortOrder": ""
						}
					],
					"children": [
						{}
					],
					"postCount": 0,
					"parentName": ""
				},
				"userInfo": {},
				"tagList": [
					{
						"id": 0,
						"tagName": "",
						"categoryId": 0,
						"color": "",
						"icon": "",
						"sort": 0,
						"enabled": 0,
						"useCount": 0,
						"isSystem": 0,
						"description": "",
						"sortOrder": ""
					}
				],
				"avatar": "",
				"isLiked": true,
				"isFavorite": true,
				"nickname": "",
				"feedbackCount": 0,
				"categoryName": "",
				"viewTime": "",
				"viewId": 0
			}
		],
		"current": 0,
		"total": 0,
		"pages": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 获取收藏的帖子


**接口地址**:`/miniapp/post/favorites`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>获取用户收藏的帖子列表</p>



**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|openId|用户OpenID|query|true|string||
|current|页码|query|false|integer(int32)||
|size|每页大小|query|false|integer(int32)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RIPageSupPostVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||IPageSupPostVO|IPageSupPostVO|
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;records||array|SupPostVO|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;location|地址|string||
|&emsp;&emsp;&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;&emsp;&emsp;title|标题|string||
|&emsp;&emsp;&emsp;&emsp;content|内容|string||
|&emsp;&emsp;&emsp;&emsp;images|图片|string||
|&emsp;&emsp;&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;&emsp;&emsp;contactName||string||
|&emsp;&emsp;&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryId||integer(int64)||
|&emsp;&emsp;&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;viewCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;&emsp;&emsp;userInfo||object||
|&emsp;&emsp;&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;avatar||string||
|&emsp;&emsp;&emsp;&emsp;isLiked||boolean||
|&emsp;&emsp;&emsp;&emsp;isFavorite||boolean||
|&emsp;&emsp;&emsp;&emsp;nickname||string||
|&emsp;&emsp;&emsp;&emsp;feedbackCount|反馈数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryName||string||
|&emsp;&emsp;&emsp;&emsp;viewTime||string(date-time)||
|&emsp;&emsp;&emsp;&emsp;viewId||integer(int64)||
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"size": 0,
		"records": [
			{
				"id": 0,
				"location": "",
				"longitude": 0,
				"latitude": 0,
				"contactType": "",
				"contactNumber": "",
				"title": "",
				"content": "",
				"images": "",
				"address": "",
				"publishTime": "",
				"publishStatus": "",
				"timeStatus": "",
				"auditStatus": "",
				"geoLocation": "",
				"tags": [],
				"contactName": "",
				"contactPhone": "",
				"top": "",
				"completed": 0,
				"categoryId": 0,
				"likeCount": 0,
				"favoriteCount": 0,
				"viewCount": 0,
				"auditRemark": "",
				"auditTime": "",
				"auditUserId": 0,
				"category": {
					"id": 0,
					"name": "",
					"parentId": 0,
					"icon": "",
					"description": "",
					"sort": 0,
					"enabled": 0,
					"enableAudit": 0,
					"tip": "",
					"maxImages": 0,
					"allowTags": "",
					"tags": [
						{
							"id": 0,
							"tagName": "",
							"categoryId": 0,
							"color": "",
							"icon": "",
							"sort": 0,
							"enabled": 0,
							"useCount": 0,
							"isSystem": 0,
							"description": "",
							"sortOrder": ""
						}
					],
					"children": [
						{}
					],
					"postCount": 0,
					"parentName": ""
				},
				"userInfo": {},
				"tagList": [
					{
						"id": 0,
						"tagName": "",
						"categoryId": 0,
						"color": "",
						"icon": "",
						"sort": 0,
						"enabled": 0,
						"useCount": 0,
						"isSystem": 0,
						"description": "",
						"sortOrder": ""
					}
				],
				"avatar": "",
				"isLiked": true,
				"isFavorite": true,
				"nickname": "",
				"feedbackCount": 0,
				"categoryName": "",
				"viewTime": "",
				"viewId": 0
			}
		],
		"current": 0,
		"total": 0,
		"pages": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 获取帖子详情


**接口地址**:`/miniapp/post/detail/{id}`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>根据帖子ID获取帖子详情</p>



**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|帖子ID|path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RSupPostVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||SupPostVO|SupPostVO|
|&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;location|地址|string||
|&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;title|标题|string||
|&emsp;&emsp;content|内容|string||
|&emsp;&emsp;images|图片|string||
|&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;contactName||string||
|&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;categoryId||integer(int64)||
|&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;viewCount||integer(int32)||
|&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;userInfo||object||
|&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;avatar||string||
|&emsp;&emsp;isLiked||boolean||
|&emsp;&emsp;isFavorite||boolean||
|&emsp;&emsp;nickname||string||
|&emsp;&emsp;feedbackCount|反馈数量|integer(int32)||
|&emsp;&emsp;categoryName||string||
|&emsp;&emsp;viewTime||string(date-time)||
|&emsp;&emsp;viewId||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"id": 0,
		"location": "",
		"longitude": 0,
		"latitude": 0,
		"contactType": "",
		"contactNumber": "",
		"title": "",
		"content": "",
		"images": "",
		"address": "",
		"publishTime": "",
		"publishStatus": "",
		"timeStatus": "",
		"auditStatus": "",
		"geoLocation": "",
		"tags": [],
		"contactName": "",
		"contactPhone": "",
		"top": "",
		"completed": 0,
		"categoryId": 0,
		"likeCount": 0,
		"favoriteCount": 0,
		"viewCount": 0,
		"auditRemark": "",
		"auditTime": "",
		"auditUserId": 0,
		"category": {
			"id": 0,
			"name": "",
			"parentId": 0,
			"icon": "",
			"description": "",
			"sort": 0,
			"enabled": 0,
			"enableAudit": 0,
			"tip": "",
			"maxImages": 0,
			"allowTags": "",
			"tags": [
				{
					"id": 0,
					"tagName": "",
					"categoryId": 0,
					"color": "",
					"icon": "",
					"sort": 0,
					"enabled": 0,
					"useCount": 0,
					"isSystem": 0,
					"description": "",
					"sortOrder": ""
				}
			],
			"children": [
				{}
			],
			"postCount": 0,
			"parentName": ""
		},
		"userInfo": {},
		"tagList": [
			{
				"id": 0,
				"tagName": "",
				"categoryId": 0,
				"color": "",
				"icon": "",
				"sort": 0,
				"enabled": 0,
				"useCount": 0,
				"isSystem": 0,
				"description": "",
				"sortOrder": ""
			}
		],
		"avatar": "",
		"isLiked": true,
		"isFavorite": true,
		"nickname": "",
		"feedbackCount": 0,
		"categoryName": "",
		"viewTime": "",
		"viewId": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


# 微信用户信息


## 详情


**接口地址**:`/blade-chat/user/info`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>通过ID查询用户个人信息</p>



**请求参数**:


暂无


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RUserProfile|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||UserProfile|UserProfile|
|&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;tenantId|租户ID|string||
|&emsp;&emsp;name|联系人姓名|string||
|&emsp;&emsp;phone|联系电话|string||
|&emsp;&emsp;wechat|微信账号|string||
|&emsp;&emsp;contactType|联系方式类型|string||
|&emsp;&emsp;nickname|昵称|string||
|&emsp;&emsp;mobile|手机号|string||
|&emsp;&emsp;gender|性别|string||
|&emsp;&emsp;signature|个性签名|string||
|&emsp;&emsp;avatar|头像|string||
|&emsp;&emsp;birthday|生日|string||
|&emsp;&emsp;region|地区|string||
|&emsp;&emsp;email|邮箱|string||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"id": 0,
		"tenantId": "",
		"name": "",
		"phone": "",
		"wechat": "",
		"contactType": "",
		"nickname": "",
		"mobile": "",
		"gender": "",
		"signature": "",
		"avatar": "",
		"birthday": "",
		"region": "",
		"email": ""
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## update


**接口地址**:`/blade-chat/user/update`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "id": 0,
  "tenantId": "",
  "name": "",
  "phone": "",
  "wechat": "",
  "contactType": "",
  "nickname": "",
  "mobile": "",
  "gender": "",
  "signature": "",
  "avatar": "",
  "birthday": "",
  "region": "",
  "email": ""
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|userProfile|UserProfile|body|true|UserProfile|UserProfile|
|&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;tenantId|租户ID||false|string||
|&emsp;&emsp;name|联系人姓名||false|string||
|&emsp;&emsp;phone|联系电话||false|string||
|&emsp;&emsp;wechat|微信账号||false|string||
|&emsp;&emsp;contactType|联系方式类型||false|string||
|&emsp;&emsp;nickname|昵称||false|string||
|&emsp;&emsp;mobile|手机号||false|string||
|&emsp;&emsp;gender|性别||false|string||
|&emsp;&emsp;signature|个性签名||false|string||
|&emsp;&emsp;avatar|头像||false|string||
|&emsp;&emsp;birthday|生日||false|string||
|&emsp;&emsp;region|地区||false|string||
|&emsp;&emsp;email|邮箱||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|R|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 获取用户统计数据


**接口地址**:`/blade-chat/user/stats`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>获取用户的帖子数、点赞数、收藏数</p>



**请求参数**:


暂无


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RMapStringObject|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


# 小程序广告接口


## 查询分类的标签


**接口地址**:`/blade-chat/post/tag/custom`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|tagCreateCommand|TagCreateCommand|body|true|TagCreateCommand|TagCreateCommand|


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|R|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 发布广告


**接口地址**:`/blade-chat/post/submit`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "id": 0,
  "location": "",
  "longitude": 0,
  "latitude": 0,
  "contactType": "",
  "contactNumber": "",
  "title": "",
  "content": "",
  "images": "",
  "address": "",
  "publishTime": "",
  "publishStatus": "",
  "timeStatus": "",
  "auditStatus": "",
  "geoLocation": "",
  "tags": [],
  "contactName": "",
  "contactPhone": "",
  "top": "",
  "completed": 0,
  "categoryId": 0,
  "likeCount": 0,
  "favoriteCount": 0,
  "viewCount": 0,
  "auditRemark": "",
  "auditTime": "",
  "auditUserId": 0,
  "category": {
    "id": 0,
    "name": "",
    "parentId": 0,
    "icon": "",
    "description": "",
    "sort": 0,
    "enabled": 0,
    "enableAudit": 0,
    "tip": "",
    "maxImages": 0,
    "allowTags": "",
    "tags": [
      {
        "id": 0,
        "tagName": "",
        "categoryId": 0,
        "color": "",
        "icon": "",
        "sort": 0,
        "enabled": 0,
        "useCount": 0,
        "isSystem": 0,
        "description": "",
        "sortOrder": ""
      }
    ],
    "children": [
      {}
    ],
    "postCount": 0,
    "parentName": ""
  },
  "userInfo": {},
  "tagList": [
    {
      "id": 0,
      "tagName": "",
      "categoryId": 0,
      "color": "",
      "icon": "",
      "sort": 0,
      "enabled": 0,
      "useCount": 0,
      "isSystem": 0,
      "description": "",
      "sortOrder": ""
    }
  ],
  "avatar": "",
  "isLiked": true,
  "isFavorite": true,
  "nickname": "",
  "feedbackCount": 0,
  "categoryName": "",
  "viewTime": "",
  "viewId": 0
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|supPostVO|百事通信息贴|body|true|SupPostVO|SupPostVO|
|&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;location|地址||false|string||
|&emsp;&emsp;longitude|经度||false|number(double)||
|&emsp;&emsp;latitude|纬度||false|number(double)||
|&emsp;&emsp;contactType|联系信息类型||false|string||
|&emsp;&emsp;contactNumber|联系编号||false|string||
|&emsp;&emsp;title|标题||false|string||
|&emsp;&emsp;content|内容||false|string||
|&emsp;&emsp;images|图片||false|string||
|&emsp;&emsp;address|发布地址||false|string||
|&emsp;&emsp;publishTime|发布时间||false|string(date-time)||
|&emsp;&emsp;publishStatus|发布状态||false|string||
|&emsp;&emsp;timeStatus|时效状态||false|string||
|&emsp;&emsp;auditStatus|审核状态||false|string||
|&emsp;&emsp;geoLocation|地理位置||false|string||
|&emsp;&emsp;tags|标签||false|array|string|
|&emsp;&emsp;contactName|||false|string||
|&emsp;&emsp;contactPhone|||false|string||
|&emsp;&emsp;top|是否置顶||false|string||
|&emsp;&emsp;completed|是否已完成||false|integer(int32)||
|&emsp;&emsp;categoryId|||false|integer(int64)||
|&emsp;&emsp;likeCount|点赞数||false|integer(int32)||
|&emsp;&emsp;favoriteCount|收藏数||false|integer(int32)||
|&emsp;&emsp;viewCount|||false|integer(int32)||
|&emsp;&emsp;auditRemark|审核备注||false|string||
|&emsp;&emsp;auditTime|审核时间||false|string(date-time)||
|&emsp;&emsp;auditUserId|审核人ID||false|integer(int64)||
|&emsp;&emsp;category|||false|Category|Category|
|&emsp;&emsp;&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;name|分类名称||false|string||
|&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID||false|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;icon|分类图标||false|string||
|&emsp;&emsp;&emsp;&emsp;description|分类描述||false|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;tip|提示信息||false|string||
|&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签||false|string||
|&emsp;&emsp;&emsp;&emsp;tags|||false|array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称||false|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID||false|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色||false|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标||false|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description|||false|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder|||false|string||
|&emsp;&emsp;&emsp;&emsp;children|||false|array|Category|
|&emsp;&emsp;&emsp;&emsp;postCount|||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;parentName|||false|string||
|&emsp;&emsp;userInfo|||false|object||
|&emsp;&emsp;tagList|||false|array|Tag|
|&emsp;&emsp;&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;tagName|标签名称||false|string||
|&emsp;&emsp;&emsp;&emsp;categoryId|分类ID||false|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;color|标签颜色||false|string||
|&emsp;&emsp;&emsp;&emsp;icon|标签图标||false|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;useCount|使用次数||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;description|||false|string||
|&emsp;&emsp;&emsp;&emsp;sortOrder|||false|string||
|&emsp;&emsp;avatar|||false|string||
|&emsp;&emsp;isLiked|||false|boolean||
|&emsp;&emsp;isFavorite|||false|boolean||
|&emsp;&emsp;nickname|||false|string||
|&emsp;&emsp;feedbackCount|反馈数量||false|integer(int32)||
|&emsp;&emsp;categoryName|||false|string||
|&emsp;&emsp;viewTime|||false|string(date-time)||
|&emsp;&emsp;viewId|||false|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 保存草稿


**接口地址**:`/blade-chat/post/save-draft`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "id": 0,
  "location": "",
  "longitude": 0,
  "latitude": 0,
  "contactType": "",
  "contactNumber": "",
  "title": "",
  "content": "",
  "images": "",
  "address": "",
  "publishTime": "",
  "publishStatus": "",
  "timeStatus": "",
  "auditStatus": "",
  "geoLocation": "",
  "tags": [],
  "contactName": "",
  "contactPhone": "",
  "top": "",
  "completed": 0,
  "categoryId": 0,
  "likeCount": 0,
  "favoriteCount": 0,
  "viewCount": 0,
  "auditRemark": "",
  "auditTime": "",
  "auditUserId": 0,
  "category": {
    "id": 0,
    "name": "",
    "parentId": 0,
    "icon": "",
    "description": "",
    "sort": 0,
    "enabled": 0,
    "enableAudit": 0,
    "tip": "",
    "maxImages": 0,
    "allowTags": "",
    "tags": [
      {
        "id": 0,
        "tagName": "",
        "categoryId": 0,
        "color": "",
        "icon": "",
        "sort": 0,
        "enabled": 0,
        "useCount": 0,
        "isSystem": 0,
        "description": "",
        "sortOrder": ""
      }
    ],
    "children": [
      {}
    ],
    "postCount": 0,
    "parentName": ""
  },
  "userInfo": {},
  "tagList": [
    {
      "id": 0,
      "tagName": "",
      "categoryId": 0,
      "color": "",
      "icon": "",
      "sort": 0,
      "enabled": 0,
      "useCount": 0,
      "isSystem": 0,
      "description": "",
      "sortOrder": ""
    }
  ],
  "avatar": "",
  "isLiked": true,
  "isFavorite": true,
  "nickname": "",
  "feedbackCount": 0,
  "categoryName": "",
  "viewTime": "",
  "viewId": 0
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|supPostVO|百事通信息贴|body|true|SupPostVO|SupPostVO|
|&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;location|地址||false|string||
|&emsp;&emsp;longitude|经度||false|number(double)||
|&emsp;&emsp;latitude|纬度||false|number(double)||
|&emsp;&emsp;contactType|联系信息类型||false|string||
|&emsp;&emsp;contactNumber|联系编号||false|string||
|&emsp;&emsp;title|标题||false|string||
|&emsp;&emsp;content|内容||false|string||
|&emsp;&emsp;images|图片||false|string||
|&emsp;&emsp;address|发布地址||false|string||
|&emsp;&emsp;publishTime|发布时间||false|string(date-time)||
|&emsp;&emsp;publishStatus|发布状态||false|string||
|&emsp;&emsp;timeStatus|时效状态||false|string||
|&emsp;&emsp;auditStatus|审核状态||false|string||
|&emsp;&emsp;geoLocation|地理位置||false|string||
|&emsp;&emsp;tags|标签||false|array|string|
|&emsp;&emsp;contactName|||false|string||
|&emsp;&emsp;contactPhone|||false|string||
|&emsp;&emsp;top|是否置顶||false|string||
|&emsp;&emsp;completed|是否已完成||false|integer(int32)||
|&emsp;&emsp;categoryId|||false|integer(int64)||
|&emsp;&emsp;likeCount|点赞数||false|integer(int32)||
|&emsp;&emsp;favoriteCount|收藏数||false|integer(int32)||
|&emsp;&emsp;viewCount|||false|integer(int32)||
|&emsp;&emsp;auditRemark|审核备注||false|string||
|&emsp;&emsp;auditTime|审核时间||false|string(date-time)||
|&emsp;&emsp;auditUserId|审核人ID||false|integer(int64)||
|&emsp;&emsp;category|||false|Category|Category|
|&emsp;&emsp;&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;name|分类名称||false|string||
|&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID||false|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;icon|分类图标||false|string||
|&emsp;&emsp;&emsp;&emsp;description|分类描述||false|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;tip|提示信息||false|string||
|&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签||false|string||
|&emsp;&emsp;&emsp;&emsp;tags|||false|array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称||false|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID||false|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色||false|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标||false|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description|||false|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder|||false|string||
|&emsp;&emsp;&emsp;&emsp;children|||false|array|Category|
|&emsp;&emsp;&emsp;&emsp;postCount|||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;parentName|||false|string||
|&emsp;&emsp;userInfo|||false|object||
|&emsp;&emsp;tagList|||false|array|Tag|
|&emsp;&emsp;&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;tagName|标签名称||false|string||
|&emsp;&emsp;&emsp;&emsp;categoryId|分类ID||false|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;color|标签颜色||false|string||
|&emsp;&emsp;&emsp;&emsp;icon|标签图标||false|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;useCount|使用次数||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;description|||false|string||
|&emsp;&emsp;&emsp;&emsp;sortOrder|||false|string||
|&emsp;&emsp;avatar|||false|string||
|&emsp;&emsp;isLiked|||false|boolean||
|&emsp;&emsp;isFavorite|||false|boolean||
|&emsp;&emsp;nickname|||false|string||
|&emsp;&emsp;feedbackCount|反馈数量||false|integer(int32)||
|&emsp;&emsp;categoryName|||false|string||
|&emsp;&emsp;viewTime|||false|string(date-time)||
|&emsp;&emsp;viewId|||false|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RLong|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|integer(int64)|integer(int64)|
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": 0,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 点赞帖子


**接口地址**:`/blade-chat/post/like/{postId}`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|postId||path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 收藏帖子


**接口地址**:`/blade-chat/post/favorite/{postId}`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|postId||path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 清空收藏


**接口地址**:`/blade-chat/post/favorite/clear`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


暂无


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 新增帖子


**接口地址**:`/blade-chat/post/create`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "images": "",
  "tags": "",
  "contactName": "",
  "contactType": "",
  "contactNumber": "",
  "content": "",
  "categoryId": 0,
  "location": "",
  "address": "",
  "longitude": 0,
  "latitude": 0
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|postCreateRequest|PostCreateRequest|body|true|PostCreateRequest|PostCreateRequest|
|&emsp;&emsp;images|||false|string||
|&emsp;&emsp;tags|||false|string||
|&emsp;&emsp;contactName|||false|string||
|&emsp;&emsp;contactType|||false|string||
|&emsp;&emsp;contactNumber|||false|string||
|&emsp;&emsp;content|||false|string||
|&emsp;&emsp;categoryId|||false|integer(int64)||
|&emsp;&emsp;location|||false|string||
|&emsp;&emsp;address|||false|string||
|&emsp;&emsp;longitude|||false|number(double)||
|&emsp;&emsp;latitude|||false|number(double)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RVoid|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 切换帖子完成状态


**接口地址**:`/blade-chat/post/completed/{id}`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 清空浏览记录


**接口地址**:`/blade-chat/post/clear-view-history`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


暂无


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|R|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 添加浏览记录


**接口地址**:`/blade-chat/post/add-view-history/{postId}`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|postId||path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|R|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 获取浏览记录


**接口地址**:`/blade-chat/post/view-history`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>分页获取浏览记录</p>



**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|current|当前页|query|false|string||
|size|每页的数量|query|false|string||
|ascs||query|false|string||
|descs||query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RIPageSupPostVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||IPageSupPostVO|IPageSupPostVO|
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;records||array|SupPostVO|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;location|地址|string||
|&emsp;&emsp;&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;&emsp;&emsp;title|标题|string||
|&emsp;&emsp;&emsp;&emsp;content|内容|string||
|&emsp;&emsp;&emsp;&emsp;images|图片|string||
|&emsp;&emsp;&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;&emsp;&emsp;contactName||string||
|&emsp;&emsp;&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryId||integer(int64)||
|&emsp;&emsp;&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;viewCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;&emsp;&emsp;userInfo||object||
|&emsp;&emsp;&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;avatar||string||
|&emsp;&emsp;&emsp;&emsp;isLiked||boolean||
|&emsp;&emsp;&emsp;&emsp;isFavorite||boolean||
|&emsp;&emsp;&emsp;&emsp;nickname||string||
|&emsp;&emsp;&emsp;&emsp;feedbackCount|反馈数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryName||string||
|&emsp;&emsp;&emsp;&emsp;viewTime||string(date-time)||
|&emsp;&emsp;&emsp;&emsp;viewId||integer(int64)||
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"size": 0,
		"records": [
			{
				"id": 0,
				"location": "",
				"longitude": 0,
				"latitude": 0,
				"contactType": "",
				"contactNumber": "",
				"title": "",
				"content": "",
				"images": "",
				"address": "",
				"publishTime": "",
				"publishStatus": "",
				"timeStatus": "",
				"auditStatus": "",
				"geoLocation": "",
				"tags": [],
				"contactName": "",
				"contactPhone": "",
				"top": "",
				"completed": 0,
				"categoryId": 0,
				"likeCount": 0,
				"favoriteCount": 0,
				"viewCount": 0,
				"auditRemark": "",
				"auditTime": "",
				"auditUserId": 0,
				"category": {
					"id": 0,
					"name": "",
					"parentId": 0,
					"icon": "",
					"description": "",
					"sort": 0,
					"enabled": 0,
					"enableAudit": 0,
					"tip": "",
					"maxImages": 0,
					"allowTags": "",
					"tags": [
						{
							"id": 0,
							"tagName": "",
							"categoryId": 0,
							"color": "",
							"icon": "",
							"sort": 0,
							"enabled": 0,
							"useCount": 0,
							"isSystem": 0,
							"description": "",
							"sortOrder": ""
						}
					],
					"children": [
						{}
					],
					"postCount": 0,
					"parentName": ""
				},
				"userInfo": {},
				"tagList": [
					{
						"id": 0,
						"tagName": "",
						"categoryId": 0,
						"color": "",
						"icon": "",
						"sort": 0,
						"enabled": 0,
						"useCount": 0,
						"isSystem": 0,
						"description": "",
						"sortOrder": ""
					}
				],
				"avatar": "",
				"isLiked": true,
				"isFavorite": true,
				"nickname": "",
				"feedbackCount": 0,
				"categoryName": "",
				"viewTime": "",
				"viewId": 0
			}
		],
		"current": 0,
		"total": 0,
		"pages": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 查询分类的标签


**接口地址**:`/blade-chat/post/tag/category/{categoryId}`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|categoryId||path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RListTag|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|array|Tag|
|&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;description||string||
|&emsp;&emsp;sortOrder||string||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": [
		{
			"id": 0,
			"tagName": "",
			"categoryId": 0,
			"color": "",
			"icon": "",
			"sort": 0,
			"enabled": 0,
			"useCount": 0,
			"isSystem": 0,
			"description": "",
			"sortOrder": ""
		}
	],
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## getMyPosts_1


**接口地址**:`/blade-chat/post/my`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|current|当前页|query|false|string||
|size|每页的数量|query|false|string||
|ascs||query|false|string||
|descs||query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RIPageSupPostVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||IPageSupPostVO|IPageSupPostVO|
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;records||array|SupPostVO|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;location|地址|string||
|&emsp;&emsp;&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;&emsp;&emsp;title|标题|string||
|&emsp;&emsp;&emsp;&emsp;content|内容|string||
|&emsp;&emsp;&emsp;&emsp;images|图片|string||
|&emsp;&emsp;&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;&emsp;&emsp;contactName||string||
|&emsp;&emsp;&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryId||integer(int64)||
|&emsp;&emsp;&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;viewCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;&emsp;&emsp;userInfo||object||
|&emsp;&emsp;&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;avatar||string||
|&emsp;&emsp;&emsp;&emsp;isLiked||boolean||
|&emsp;&emsp;&emsp;&emsp;isFavorite||boolean||
|&emsp;&emsp;&emsp;&emsp;nickname||string||
|&emsp;&emsp;&emsp;&emsp;feedbackCount|反馈数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryName||string||
|&emsp;&emsp;&emsp;&emsp;viewTime||string(date-time)||
|&emsp;&emsp;&emsp;&emsp;viewId||integer(int64)||
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"size": 0,
		"records": [
			{
				"id": 0,
				"location": "",
				"longitude": 0,
				"latitude": 0,
				"contactType": "",
				"contactNumber": "",
				"title": "",
				"content": "",
				"images": "",
				"address": "",
				"publishTime": "",
				"publishStatus": "",
				"timeStatus": "",
				"auditStatus": "",
				"geoLocation": "",
				"tags": [],
				"contactName": "",
				"contactPhone": "",
				"top": "",
				"completed": 0,
				"categoryId": 0,
				"likeCount": 0,
				"favoriteCount": 0,
				"viewCount": 0,
				"auditRemark": "",
				"auditTime": "",
				"auditUserId": 0,
				"category": {
					"id": 0,
					"name": "",
					"parentId": 0,
					"icon": "",
					"description": "",
					"sort": 0,
					"enabled": 0,
					"enableAudit": 0,
					"tip": "",
					"maxImages": 0,
					"allowTags": "",
					"tags": [
						{
							"id": 0,
							"tagName": "",
							"categoryId": 0,
							"color": "",
							"icon": "",
							"sort": 0,
							"enabled": 0,
							"useCount": 0,
							"isSystem": 0,
							"description": "",
							"sortOrder": ""
						}
					],
					"children": [
						{}
					],
					"postCount": 0,
					"parentName": ""
				},
				"userInfo": {},
				"tagList": [
					{
						"id": 0,
						"tagName": "",
						"categoryId": 0,
						"color": "",
						"icon": "",
						"sort": 0,
						"enabled": 0,
						"useCount": 0,
						"isSystem": 0,
						"description": "",
						"sortOrder": ""
					}
				],
				"avatar": "",
				"isLiked": true,
				"isFavorite": true,
				"nickname": "",
				"feedbackCount": 0,
				"categoryName": "",
				"viewTime": "",
				"viewId": 0
			}
		],
		"current": 0,
		"total": 0,
		"pages": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## getLikedPosts


**接口地址**:`/blade-chat/post/liked`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|current|当前页|query|false|string||
|size|每页的数量|query|false|string||
|ascs||query|false|string||
|descs||query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RIPageSupPostVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||IPageSupPostVO|IPageSupPostVO|
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;records||array|SupPostVO|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;location|地址|string||
|&emsp;&emsp;&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;&emsp;&emsp;title|标题|string||
|&emsp;&emsp;&emsp;&emsp;content|内容|string||
|&emsp;&emsp;&emsp;&emsp;images|图片|string||
|&emsp;&emsp;&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;&emsp;&emsp;contactName||string||
|&emsp;&emsp;&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryId||integer(int64)||
|&emsp;&emsp;&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;viewCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;&emsp;&emsp;userInfo||object||
|&emsp;&emsp;&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;avatar||string||
|&emsp;&emsp;&emsp;&emsp;isLiked||boolean||
|&emsp;&emsp;&emsp;&emsp;isFavorite||boolean||
|&emsp;&emsp;&emsp;&emsp;nickname||string||
|&emsp;&emsp;&emsp;&emsp;feedbackCount|反馈数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryName||string||
|&emsp;&emsp;&emsp;&emsp;viewTime||string(date-time)||
|&emsp;&emsp;&emsp;&emsp;viewId||integer(int64)||
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"size": 0,
		"records": [
			{
				"id": 0,
				"location": "",
				"longitude": 0,
				"latitude": 0,
				"contactType": "",
				"contactNumber": "",
				"title": "",
				"content": "",
				"images": "",
				"address": "",
				"publishTime": "",
				"publishStatus": "",
				"timeStatus": "",
				"auditStatus": "",
				"geoLocation": "",
				"tags": [],
				"contactName": "",
				"contactPhone": "",
				"top": "",
				"completed": 0,
				"categoryId": 0,
				"likeCount": 0,
				"favoriteCount": 0,
				"viewCount": 0,
				"auditRemark": "",
				"auditTime": "",
				"auditUserId": 0,
				"category": {
					"id": 0,
					"name": "",
					"parentId": 0,
					"icon": "",
					"description": "",
					"sort": 0,
					"enabled": 0,
					"enableAudit": 0,
					"tip": "",
					"maxImages": 0,
					"allowTags": "",
					"tags": [
						{
							"id": 0,
							"tagName": "",
							"categoryId": 0,
							"color": "",
							"icon": "",
							"sort": 0,
							"enabled": 0,
							"useCount": 0,
							"isSystem": 0,
							"description": "",
							"sortOrder": ""
						}
					],
					"children": [
						{}
					],
					"postCount": 0,
					"parentName": ""
				},
				"userInfo": {},
				"tagList": [
					{
						"id": 0,
						"tagName": "",
						"categoryId": 0,
						"color": "",
						"icon": "",
						"sort": 0,
						"enabled": 0,
						"useCount": 0,
						"isSystem": 0,
						"description": "",
						"sortOrder": ""
					}
				],
				"avatar": "",
				"isLiked": true,
				"isFavorite": true,
				"nickname": "",
				"feedbackCount": 0,
				"categoryName": "",
				"viewTime": "",
				"viewId": 0
			}
		],
		"current": 0,
		"total": 0,
		"pages": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## getFavoritePosts_1


**接口地址**:`/blade-chat/post/favorite`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|current|当前页|query|false|string||
|size|每页的数量|query|false|string||
|ascs||query|false|string||
|descs||query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RIPageSupPostVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||IPageSupPostVO|IPageSupPostVO|
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;records||array|SupPostVO|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;location|地址|string||
|&emsp;&emsp;&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;&emsp;&emsp;title|标题|string||
|&emsp;&emsp;&emsp;&emsp;content|内容|string||
|&emsp;&emsp;&emsp;&emsp;images|图片|string||
|&emsp;&emsp;&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;&emsp;&emsp;contactName||string||
|&emsp;&emsp;&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryId||integer(int64)||
|&emsp;&emsp;&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;viewCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;&emsp;&emsp;userInfo||object||
|&emsp;&emsp;&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;avatar||string||
|&emsp;&emsp;&emsp;&emsp;isLiked||boolean||
|&emsp;&emsp;&emsp;&emsp;isFavorite||boolean||
|&emsp;&emsp;&emsp;&emsp;nickname||string||
|&emsp;&emsp;&emsp;&emsp;feedbackCount|反馈数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryName||string||
|&emsp;&emsp;&emsp;&emsp;viewTime||string(date-time)||
|&emsp;&emsp;&emsp;&emsp;viewId||integer(int64)||
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"size": 0,
		"records": [
			{
				"id": 0,
				"location": "",
				"longitude": 0,
				"latitude": 0,
				"contactType": "",
				"contactNumber": "",
				"title": "",
				"content": "",
				"images": "",
				"address": "",
				"publishTime": "",
				"publishStatus": "",
				"timeStatus": "",
				"auditStatus": "",
				"geoLocation": "",
				"tags": [],
				"contactName": "",
				"contactPhone": "",
				"top": "",
				"completed": 0,
				"categoryId": 0,
				"likeCount": 0,
				"favoriteCount": 0,
				"viewCount": 0,
				"auditRemark": "",
				"auditTime": "",
				"auditUserId": 0,
				"category": {
					"id": 0,
					"name": "",
					"parentId": 0,
					"icon": "",
					"description": "",
					"sort": 0,
					"enabled": 0,
					"enableAudit": 0,
					"tip": "",
					"maxImages": 0,
					"allowTags": "",
					"tags": [
						{
							"id": 0,
							"tagName": "",
							"categoryId": 0,
							"color": "",
							"icon": "",
							"sort": 0,
							"enabled": 0,
							"useCount": 0,
							"isSystem": 0,
							"description": "",
							"sortOrder": ""
						}
					],
					"children": [
						{}
					],
					"postCount": 0,
					"parentName": ""
				},
				"userInfo": {},
				"tagList": [
					{
						"id": 0,
						"tagName": "",
						"categoryId": 0,
						"color": "",
						"icon": "",
						"sort": 0,
						"enabled": 0,
						"useCount": 0,
						"isSystem": 0,
						"description": "",
						"sortOrder": ""
					}
				],
				"avatar": "",
				"isLiked": true,
				"isFavorite": true,
				"nickname": "",
				"feedbackCount": 0,
				"categoryName": "",
				"viewTime": "",
				"viewId": 0
			}
		],
		"current": 0,
		"total": 0,
		"pages": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 获取草稿详情


**接口地址**:`/blade-chat/post/draft/{id}`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RSupPostVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||SupPostVO|SupPostVO|
|&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;location|地址|string||
|&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;title|标题|string||
|&emsp;&emsp;content|内容|string||
|&emsp;&emsp;images|图片|string||
|&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;contactName||string||
|&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;categoryId||integer(int64)||
|&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;viewCount||integer(int32)||
|&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;userInfo||object||
|&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;avatar||string||
|&emsp;&emsp;isLiked||boolean||
|&emsp;&emsp;isFavorite||boolean||
|&emsp;&emsp;nickname||string||
|&emsp;&emsp;feedbackCount|反馈数量|integer(int32)||
|&emsp;&emsp;categoryName||string||
|&emsp;&emsp;viewTime||string(date-time)||
|&emsp;&emsp;viewId||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"id": 0,
		"location": "",
		"longitude": 0,
		"latitude": 0,
		"contactType": "",
		"contactNumber": "",
		"title": "",
		"content": "",
		"images": "",
		"address": "",
		"publishTime": "",
		"publishStatus": "",
		"timeStatus": "",
		"auditStatus": "",
		"geoLocation": "",
		"tags": [],
		"contactName": "",
		"contactPhone": "",
		"top": "",
		"completed": 0,
		"categoryId": 0,
		"likeCount": 0,
		"favoriteCount": 0,
		"viewCount": 0,
		"auditRemark": "",
		"auditTime": "",
		"auditUserId": 0,
		"category": {
			"id": 0,
			"name": "",
			"parentId": 0,
			"icon": "",
			"description": "",
			"sort": 0,
			"enabled": 0,
			"enableAudit": 0,
			"tip": "",
			"maxImages": 0,
			"allowTags": "",
			"tags": [
				{
					"id": 0,
					"tagName": "",
					"categoryId": 0,
					"color": "",
					"icon": "",
					"sort": 0,
					"enabled": 0,
					"useCount": 0,
					"isSystem": 0,
					"description": "",
					"sortOrder": ""
				}
			],
			"children": [
				{}
			],
			"postCount": 0,
			"parentName": ""
		},
		"userInfo": {},
		"tagList": [
			{
				"id": 0,
				"tagName": "",
				"categoryId": 0,
				"color": "",
				"icon": "",
				"sort": 0,
				"enabled": 0,
				"useCount": 0,
				"isSystem": 0,
				"description": "",
				"sortOrder": ""
			}
		],
		"avatar": "",
		"isLiked": true,
		"isFavorite": true,
		"nickname": "",
		"feedbackCount": 0,
		"categoryName": "",
		"viewTime": "",
		"viewId": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 删除草稿


**接口地址**:`/blade-chat/post/draft/{id}`


**请求方式**:`DELETE`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 获取草稿列表


**接口地址**:`/blade-chat/post/draft-list`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|current|当前页|query|false|string||
|size|每页的数量|query|false|string||
|ascs||query|false|string||
|descs||query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RIPageSupPostVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||IPageSupPostVO|IPageSupPostVO|
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;records||array|SupPostVO|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;location|地址|string||
|&emsp;&emsp;&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;&emsp;&emsp;title|标题|string||
|&emsp;&emsp;&emsp;&emsp;content|内容|string||
|&emsp;&emsp;&emsp;&emsp;images|图片|string||
|&emsp;&emsp;&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;&emsp;&emsp;contactName||string||
|&emsp;&emsp;&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryId||integer(int64)||
|&emsp;&emsp;&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;viewCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;&emsp;&emsp;userInfo||object||
|&emsp;&emsp;&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;avatar||string||
|&emsp;&emsp;&emsp;&emsp;isLiked||boolean||
|&emsp;&emsp;&emsp;&emsp;isFavorite||boolean||
|&emsp;&emsp;&emsp;&emsp;nickname||string||
|&emsp;&emsp;&emsp;&emsp;feedbackCount|反馈数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryName||string||
|&emsp;&emsp;&emsp;&emsp;viewTime||string(date-time)||
|&emsp;&emsp;&emsp;&emsp;viewId||integer(int64)||
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"size": 0,
		"records": [
			{
				"id": 0,
				"location": "",
				"longitude": 0,
				"latitude": 0,
				"contactType": "",
				"contactNumber": "",
				"title": "",
				"content": "",
				"images": "",
				"address": "",
				"publishTime": "",
				"publishStatus": "",
				"timeStatus": "",
				"auditStatus": "",
				"geoLocation": "",
				"tags": [],
				"contactName": "",
				"contactPhone": "",
				"top": "",
				"completed": 0,
				"categoryId": 0,
				"likeCount": 0,
				"favoriteCount": 0,
				"viewCount": 0,
				"auditRemark": "",
				"auditTime": "",
				"auditUserId": 0,
				"category": {
					"id": 0,
					"name": "",
					"parentId": 0,
					"icon": "",
					"description": "",
					"sort": 0,
					"enabled": 0,
					"enableAudit": 0,
					"tip": "",
					"maxImages": 0,
					"allowTags": "",
					"tags": [
						{
							"id": 0,
							"tagName": "",
							"categoryId": 0,
							"color": "",
							"icon": "",
							"sort": 0,
							"enabled": 0,
							"useCount": 0,
							"isSystem": 0,
							"description": "",
							"sortOrder": ""
						}
					],
					"children": [
						{}
					],
					"postCount": 0,
					"parentName": ""
				},
				"userInfo": {},
				"tagList": [
					{
						"id": 0,
						"tagName": "",
						"categoryId": 0,
						"color": "",
						"icon": "",
						"sort": 0,
						"enabled": 0,
						"useCount": 0,
						"isSystem": 0,
						"description": "",
						"sortOrder": ""
					}
				],
				"avatar": "",
				"isLiked": true,
				"isFavorite": true,
				"nickname": "",
				"feedbackCount": 0,
				"categoryName": "",
				"viewTime": "",
				"viewId": 0
			}
		],
		"current": 0,
		"total": 0,
		"pages": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 删除帖子


**接口地址**:`/blade-chat/post/{id}`


**请求方式**:`DELETE`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 删除浏览记录


**接口地址**:`/blade-chat/post/view-history/{id}`


**请求方式**:`DELETE`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


# 小程序拨号记录接口


## 清空拨号记录


**接口地址**:`/blade-chat/post/clear-call-history`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


暂无


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 添加拨号记录


**接口地址**:`/blade-chat/post/add-call-history/{postId}`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|postId||path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 获取拨号记录


**接口地址**:`/blade-chat/post/call-history`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|current|当前页|query|false|string||
|size|每页的数量|query|false|string||
|ascs||query|false|string||
|descs||query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RIPageSupPostVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||IPageSupPostVO|IPageSupPostVO|
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;records||array|SupPostVO|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;location|地址|string||
|&emsp;&emsp;&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;&emsp;&emsp;title|标题|string||
|&emsp;&emsp;&emsp;&emsp;content|内容|string||
|&emsp;&emsp;&emsp;&emsp;images|图片|string||
|&emsp;&emsp;&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;&emsp;&emsp;contactName||string||
|&emsp;&emsp;&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryId||integer(int64)||
|&emsp;&emsp;&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;viewCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;&emsp;&emsp;userInfo||object||
|&emsp;&emsp;&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;avatar||string||
|&emsp;&emsp;&emsp;&emsp;isLiked||boolean||
|&emsp;&emsp;&emsp;&emsp;isFavorite||boolean||
|&emsp;&emsp;&emsp;&emsp;nickname||string||
|&emsp;&emsp;&emsp;&emsp;feedbackCount|反馈数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryName||string||
|&emsp;&emsp;&emsp;&emsp;viewTime||string(date-time)||
|&emsp;&emsp;&emsp;&emsp;viewId||integer(int64)||
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"size": 0,
		"records": [
			{
				"id": 0,
				"location": "",
				"longitude": 0,
				"latitude": 0,
				"contactType": "",
				"contactNumber": "",
				"title": "",
				"content": "",
				"images": "",
				"address": "",
				"publishTime": "",
				"publishStatus": "",
				"timeStatus": "",
				"auditStatus": "",
				"geoLocation": "",
				"tags": [],
				"contactName": "",
				"contactPhone": "",
				"top": "",
				"completed": 0,
				"categoryId": 0,
				"likeCount": 0,
				"favoriteCount": 0,
				"viewCount": 0,
				"auditRemark": "",
				"auditTime": "",
				"auditUserId": 0,
				"category": {
					"id": 0,
					"name": "",
					"parentId": 0,
					"icon": "",
					"description": "",
					"sort": 0,
					"enabled": 0,
					"enableAudit": 0,
					"tip": "",
					"maxImages": 0,
					"allowTags": "",
					"tags": [
						{
							"id": 0,
							"tagName": "",
							"categoryId": 0,
							"color": "",
							"icon": "",
							"sort": 0,
							"enabled": 0,
							"useCount": 0,
							"isSystem": 0,
							"description": "",
							"sortOrder": ""
						}
					],
					"children": [
						{}
					],
					"postCount": 0,
					"parentName": ""
				},
				"userInfo": {},
				"tagList": [
					{
						"id": 0,
						"tagName": "",
						"categoryId": 0,
						"color": "",
						"icon": "",
						"sort": 0,
						"enabled": 0,
						"useCount": 0,
						"isSystem": 0,
						"description": "",
						"sortOrder": ""
					}
				],
				"avatar": "",
				"isLiked": true,
				"isFavorite": true,
				"nickname": "",
				"feedbackCount": 0,
				"categoryName": "",
				"viewTime": "",
				"viewId": 0
			}
		],
		"current": 0,
		"total": 0,
		"pages": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


# 小程序反馈记录接口


## 新增


**接口地址**:`/blade-chat/feedback/save`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>传入report</p>



**请求示例**:


```javascript
{
  "id": 0,
  "postId": 0,
  "userId": 0,
  "content": "",
  "images": "",
  "auditStatus": "",
  "reason": ""
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|report|举报记录|body|true|Report|Report|
|&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;postId|信息贴ID||false|integer(int64)||
|&emsp;&emsp;userId|举报用户ID||false|integer(int64)||
|&emsp;&emsp;content|举报内容||false|string||
|&emsp;&emsp;images|举报图片||false|string||
|&emsp;&emsp;auditStatus|审核状态||false|string||
|&emsp;&emsp;reason|理由||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RLong|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|integer(int64)|integer(int64)|
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": 0,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 新增


**接口地址**:`/blade-chat/feedback/painpoint/save`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>传入painPoint</p>



**请求示例**:


```javascript
{
  "id": 0,
  "content": "",
  "image": "",
  "contactInfo": "",
  "auditStatus": "",
  "auditResult": "",
  "nickname": ""
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|painPoint|PainPoint对象|body|true|PainPoint|PainPoint|
|&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;content|反馈内容||false|string||
|&emsp;&emsp;image|图片路径或链接||false|string||
|&emsp;&emsp;contactInfo|联系方式||false|string||
|&emsp;&emsp;auditStatus|处理状态||false|string||
|&emsp;&emsp;auditResult|处理结果||false|string||
|&emsp;&emsp;nickname|反馈人姓名||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|R|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 新增或修改


**接口地址**:`/blade-chat/feedback/report/submit`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:<p>传入report</p>



**请求示例**:


```javascript
{
  "id": 0,
  "postId": 0,
  "userId": 0,
  "content": "",
  "images": "",
  "auditStatus": "",
  "reason": ""
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|report|举报记录|body|true|Report|Report|
|&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;postId|信息贴ID||false|integer(int64)||
|&emsp;&emsp;userId|举报用户ID||false|integer(int64)||
|&emsp;&emsp;content|举报内容||false|string||
|&emsp;&emsp;images|举报图片||false|string||
|&emsp;&emsp;auditStatus|审核状态||false|string||
|&emsp;&emsp;reason|理由||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RLong|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|integer(int64)|integer(int64)|
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": 0,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 根据分类Id查询反馈标签


**接口地址**:`/blade-chat/feedback/getTagsByCategory`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>传入categoryId</p>



**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|categoryId||query|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RListMapStringObject|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|array||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": [],
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 提交反馈


**接口地址**:`/blade-chat/feedback/submit`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded,application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "id": 0,
  "postId": 0,
  "userId": 0,
  "content": "",
  "auditStatus": "",
  "reason": ""
}
```


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|feedback|用户反馈|body|true|Feedback|Feedback|
|&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;postId|信息贴ID||false|integer(int64)||
|&emsp;&emsp;userId|反馈用户ID||false|integer(int64)||
|&emsp;&emsp;content|反馈内容||false|string||
|&emsp;&emsp;auditStatus|审核状态||false|string||
|&emsp;&emsp;reason|理由||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RLong|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|integer(int64)|integer(int64)|
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": 0,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 标记反馈有帮助


**接口地址**:`/blade-chat/feedback/helpful/{id}`


**请求方式**:`POST`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 分页查询


**接口地址**:`/blade-chat-open/feedback/page`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|helpfulCount|有帮助标记数量|query|false|string||
|nickname||query|false|string||
|avatar||query|false|string||
|postId|信息贴ID|query|false|string||
|userId|反馈用户ID|query|false|string||
|content|反馈内容|query|false|string||
|auditStatus|审核状态|query|false|string||
|reason|理由|query|false|string||
|id|主键|query|false|string||
|current|当前页|query|false|string||
|size|每页的数量|query|false|string||
|ascs||query|false|string||
|descs||query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RIPageFeedbackVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||IPageFeedbackVO|IPageFeedbackVO|
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;records||array|FeedbackVO|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;postId|信息贴ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;userId|反馈用户ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;content|反馈内容|string||
|&emsp;&emsp;&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;&emsp;&emsp;reason|理由|string||
|&emsp;&emsp;&emsp;&emsp;helpfulCount|有帮助标记数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;nickname||string||
|&emsp;&emsp;&emsp;&emsp;avatar||string||
|&emsp;&emsp;&emsp;&emsp;helpful||boolean||
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"size": 0,
		"records": [
			{
				"id": 0,
				"postId": 0,
				"userId": 0,
				"content": "",
				"auditStatus": "",
				"reason": "",
				"helpfulCount": 0,
				"nickname": "",
				"avatar": "",
				"helpful": true
			}
		],
		"current": 0,
		"total": 0,
		"pages": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 删除反馈


**接口地址**:`/blade-chat/feedback/remove`


**请求方式**:`DELETE`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 删除反馈


**接口地址**:`/blade-chat/feedback/remove/{id}`


**请求方式**:`DELETE`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||path|true|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RBoolean|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|boolean||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": true,
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


# 小程序广告开放接口


## 根据分类Id来条件分页查询帖子


**接口地址**:`/blade-chat-open/post/list`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|postCategoryId||query|false|integer(int64)||
|current|当前页|query|false|string||
|size|每页的数量|query|false|string||
|ascs||query|false|string||
|descs||query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RIPageSupPostVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||IPageSupPostVO|IPageSupPostVO|
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;records||array|SupPostVO|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;location|地址|string||
|&emsp;&emsp;&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;&emsp;&emsp;title|标题|string||
|&emsp;&emsp;&emsp;&emsp;content|内容|string||
|&emsp;&emsp;&emsp;&emsp;images|图片|string||
|&emsp;&emsp;&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;&emsp;&emsp;contactName||string||
|&emsp;&emsp;&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryId||integer(int64)||
|&emsp;&emsp;&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;viewCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;&emsp;&emsp;userInfo||object||
|&emsp;&emsp;&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;avatar||string||
|&emsp;&emsp;&emsp;&emsp;isLiked||boolean||
|&emsp;&emsp;&emsp;&emsp;isFavorite||boolean||
|&emsp;&emsp;&emsp;&emsp;nickname||string||
|&emsp;&emsp;&emsp;&emsp;feedbackCount|反馈数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryName||string||
|&emsp;&emsp;&emsp;&emsp;viewTime||string(date-time)||
|&emsp;&emsp;&emsp;&emsp;viewId||integer(int64)||
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"size": 0,
		"records": [
			{
				"id": 0,
				"location": "",
				"longitude": 0,
				"latitude": 0,
				"contactType": "",
				"contactNumber": "",
				"title": "",
				"content": "",
				"images": "",
				"address": "",
				"publishTime": "",
				"publishStatus": "",
				"timeStatus": "",
				"auditStatus": "",
				"geoLocation": "",
				"tags": [],
				"contactName": "",
				"contactPhone": "",
				"top": "",
				"completed": 0,
				"categoryId": 0,
				"likeCount": 0,
				"favoriteCount": 0,
				"viewCount": 0,
				"auditRemark": "",
				"auditTime": "",
				"auditUserId": 0,
				"category": {
					"id": 0,
					"name": "",
					"parentId": 0,
					"icon": "",
					"description": "",
					"sort": 0,
					"enabled": 0,
					"enableAudit": 0,
					"tip": "",
					"maxImages": 0,
					"allowTags": "",
					"tags": [
						{
							"id": 0,
							"tagName": "",
							"categoryId": 0,
							"color": "",
							"icon": "",
							"sort": 0,
							"enabled": 0,
							"useCount": 0,
							"isSystem": 0,
							"description": "",
							"sortOrder": ""
						}
					],
					"children": [
						{}
					],
					"postCount": 0,
					"parentName": ""
				},
				"userInfo": {},
				"tagList": [
					{
						"id": 0,
						"tagName": "",
						"categoryId": 0,
						"color": "",
						"icon": "",
						"sort": 0,
						"enabled": 0,
						"useCount": 0,
						"isSystem": 0,
						"description": "",
						"sortOrder": ""
					}
				],
				"avatar": "",
				"isLiked": true,
				"isFavorite": true,
				"nickname": "",
				"feedbackCount": 0,
				"categoryName": "",
				"viewTime": "",
				"viewId": 0
			}
		],
		"current": 0,
		"total": 0,
		"pages": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 获取帖子列表


**接口地址**:`/blade-chat-open/post/home-list`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|current|当前页|query|false|string||
|size|每页的数量|query|false|string||
|ascs||query|false|string||
|descs||query|false|string||
|isLiked||query|false|boolean||
|isFavorite||query|false|boolean||
|nickname||query|false|string||
|avatar||query|false|string||
|likeCount|点赞数|query|false|string||
|feedbackCount|反馈数量|query|false|string||
|favoriteCount|收藏数|query|false|string||
|viewCount|浏览数|query|false|integer(int32)||
|categoryId|分类ID|query|false|integer(int64)||
|categoryName||query|false|string||
|viewTime||query|false|string(date-time)||
|viewId||query|false|integer(int64)||
|location|地址|query|false|string||
|longitude|经度|query|false|string||
|latitude|纬度|query|false|string||
|contactType|联系信息类型|query|false|string||
|contactNumber|联系编号|query|false|string||
|title|标题|query|false|string||
|content|内容|query|false|string||
|images|图片|query|false|string||
|address|发布地址|query|false|string||
|publishTime|发布时间|query|false|string||
|publishStatus|发布状态|query|false|string||
|timeStatus|时效状态|query|false|string||
|auditStatus|审核状态|query|false|string||
|geoLocation|地理位置|query|false|string||
|tags|标签|query|false|string||
|contactName||query|false|string||
|contactPhone||query|false|string||
|top|是否置顶|query|false|string||
|completed|是否已完成|query|false|string||
|auditRemark|审核备注|query|false|string||
|auditTime|审核时间|query|false|string||
|auditUserId|审核人ID|query|false|string||
|category.name|分类名称|query|false|string||
|category.parentId|上级分类ID|query|false|string||
|category.icon|分类图标|query|false|string||
|category.description|分类描述|query|false|string||
|category.sort|排序|query|false|string||
|category.enabled|是否启用|query|false|string||
|category.enableAudit|是否启用审核|query|false|string||
|category.tip|提示信息|query|false|string||
|category.maxImages|最大图片数|query|false|string||
|category.allowTags|允许的标签|query|false|string||
|category.tags|标签|query|false|array|Tag|
|&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;tagName|标签名称||false|string||
|&emsp;&emsp;categoryId|分类ID||false|integer(int64)||
|&emsp;&emsp;color|标签颜色||false|string||
|&emsp;&emsp;icon|标签图标||false|string||
|&emsp;&emsp;sort|排序||false|integer(int32)||
|&emsp;&emsp;enabled|是否启用||false|integer(int32)||
|&emsp;&emsp;useCount|使用次数||false|integer(int32)||
|&emsp;&emsp;isSystem|是否系统标签||false|integer(int32)||
|&emsp;&emsp;description|||false|string||
|&emsp;&emsp;sortOrder|||false|string||
|category.children|广告分类|query|false|array|Category|
|&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;name|分类名称||false|string||
|&emsp;&emsp;parentId|上级分类ID||false|integer(int64)||
|&emsp;&emsp;icon|分类图标||false|string||
|&emsp;&emsp;description|分类描述||false|string||
|&emsp;&emsp;sort|排序||false|integer(int32)||
|&emsp;&emsp;enabled|是否启用||false|integer(int32)||
|&emsp;&emsp;enableAudit|是否启用审核||false|integer(int32)||
|&emsp;&emsp;tip|提示信息||false|string||
|&emsp;&emsp;maxImages|最大图片数||false|integer(int32)||
|&emsp;&emsp;allowTags|允许的标签||false|string||
|&emsp;&emsp;tags|||false|array|Tag|
|&emsp;&emsp;&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;tagName|标签名称||false|string||
|&emsp;&emsp;&emsp;&emsp;categoryId|分类ID||false|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;color|标签颜色||false|string||
|&emsp;&emsp;&emsp;&emsp;icon|标签图标||false|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;useCount|使用次数||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签||false|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;description|||false|string||
|&emsp;&emsp;&emsp;&emsp;sortOrder|||false|string||
|&emsp;&emsp;children|||false|array|Category|
|&emsp;&emsp;postCount|||false|integer(int32)||
|&emsp;&emsp;parentName|||false|string||
|category.postCount||query|false|integer(int32)||
|category.parentName||query|false|string||
|category.id|主键|query|false|string||
|userInfo||query|false|object||
|tagList|标签|query|false|array|Tag|
|&emsp;&emsp;id|主键||false|integer(int64)||
|&emsp;&emsp;tagName|标签名称||false|string||
|&emsp;&emsp;categoryId|分类ID||false|integer(int64)||
|&emsp;&emsp;color|标签颜色||false|string||
|&emsp;&emsp;icon|标签图标||false|string||
|&emsp;&emsp;sort|排序||false|integer(int32)||
|&emsp;&emsp;enabled|是否启用||false|integer(int32)||
|&emsp;&emsp;useCount|使用次数||false|integer(int32)||
|&emsp;&emsp;isSystem|是否系统标签||false|integer(int32)||
|&emsp;&emsp;description|||false|string||
|&emsp;&emsp;sortOrder|||false|string||
|id|主键|query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RIPageSupPostVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||IPageSupPostVO|IPageSupPostVO|
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;records||array|SupPostVO|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;location|地址|string||
|&emsp;&emsp;&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;&emsp;&emsp;title|标题|string||
|&emsp;&emsp;&emsp;&emsp;content|内容|string||
|&emsp;&emsp;&emsp;&emsp;images|图片|string||
|&emsp;&emsp;&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;&emsp;&emsp;contactName||string||
|&emsp;&emsp;&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryId||integer(int64)||
|&emsp;&emsp;&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;viewCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;&emsp;&emsp;userInfo||object||
|&emsp;&emsp;&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;avatar||string||
|&emsp;&emsp;&emsp;&emsp;isLiked||boolean||
|&emsp;&emsp;&emsp;&emsp;isFavorite||boolean||
|&emsp;&emsp;&emsp;&emsp;nickname||string||
|&emsp;&emsp;&emsp;&emsp;feedbackCount|反馈数量|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;categoryName||string||
|&emsp;&emsp;&emsp;&emsp;viewTime||string(date-time)||
|&emsp;&emsp;&emsp;&emsp;viewId||integer(int64)||
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"size": 0,
		"records": [
			{
				"id": 0,
				"location": "",
				"longitude": 0,
				"latitude": 0,
				"contactType": "",
				"contactNumber": "",
				"title": "",
				"content": "",
				"images": "",
				"address": "",
				"publishTime": "",
				"publishStatus": "",
				"timeStatus": "",
				"auditStatus": "",
				"geoLocation": "",
				"tags": [],
				"contactName": "",
				"contactPhone": "",
				"top": "",
				"completed": 0,
				"categoryId": 0,
				"likeCount": 0,
				"favoriteCount": 0,
				"viewCount": 0,
				"auditRemark": "",
				"auditTime": "",
				"auditUserId": 0,
				"category": {
					"id": 0,
					"name": "",
					"parentId": 0,
					"icon": "",
					"description": "",
					"sort": 0,
					"enabled": 0,
					"enableAudit": 0,
					"tip": "",
					"maxImages": 0,
					"allowTags": "",
					"tags": [
						{
							"id": 0,
							"tagName": "",
							"categoryId": 0,
							"color": "",
							"icon": "",
							"sort": 0,
							"enabled": 0,
							"useCount": 0,
							"isSystem": 0,
							"description": "",
							"sortOrder": ""
						}
					],
					"children": [
						{}
					],
					"postCount": 0,
					"parentName": ""
				},
				"userInfo": {},
				"tagList": [
					{
						"id": 0,
						"tagName": "",
						"categoryId": 0,
						"color": "",
						"icon": "",
						"sort": 0,
						"enabled": 0,
						"useCount": 0,
						"isSystem": 0,
						"description": "",
						"sortOrder": ""
					}
				],
				"avatar": "",
				"isLiked": true,
				"isFavorite": true,
				"nickname": "",
				"feedbackCount": 0,
				"categoryName": "",
				"viewTime": "",
				"viewId": 0
			}
		],
		"current": 0,
		"total": 0,
		"pages": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 获取帖子详情


**接口地址**:`/blade-chat-open/post/detail/{id}`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||path|true|integer(int64)||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RSupPostVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||SupPostVO|SupPostVO|
|&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;location|地址|string||
|&emsp;&emsp;longitude|经度|number(double)||
|&emsp;&emsp;latitude|纬度|number(double)||
|&emsp;&emsp;contactType|联系信息类型|string||
|&emsp;&emsp;contactNumber|联系编号|string||
|&emsp;&emsp;title|标题|string||
|&emsp;&emsp;content|内容|string||
|&emsp;&emsp;images|图片|string||
|&emsp;&emsp;address|发布地址|string||
|&emsp;&emsp;publishTime|发布时间|string(date-time)||
|&emsp;&emsp;publishStatus|发布状态|string||
|&emsp;&emsp;timeStatus|时效状态|string||
|&emsp;&emsp;auditStatus|审核状态|string||
|&emsp;&emsp;geoLocation|地理位置|string||
|&emsp;&emsp;tags|标签|array|string|
|&emsp;&emsp;contactName||string||
|&emsp;&emsp;contactPhone||string||
|&emsp;&emsp;top|是否置顶|string||
|&emsp;&emsp;completed|是否已完成|integer(int32)||
|&emsp;&emsp;categoryId||integer(int64)||
|&emsp;&emsp;likeCount|点赞数|integer(int32)||
|&emsp;&emsp;favoriteCount|收藏数|integer(int32)||
|&emsp;&emsp;viewCount||integer(int32)||
|&emsp;&emsp;auditRemark|审核备注|string||
|&emsp;&emsp;auditTime|审核时间|string(date-time)||
|&emsp;&emsp;auditUserId|审核人ID|integer(int64)||
|&emsp;&emsp;category||Category|Category|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;userInfo||object||
|&emsp;&emsp;tagList||array|Tag|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;avatar||string||
|&emsp;&emsp;isLiked||boolean||
|&emsp;&emsp;isFavorite||boolean||
|&emsp;&emsp;nickname||string||
|&emsp;&emsp;feedbackCount|反馈数量|integer(int32)||
|&emsp;&emsp;categoryName||string||
|&emsp;&emsp;viewTime||string(date-time)||
|&emsp;&emsp;viewId||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"id": 0,
		"location": "",
		"longitude": 0,
		"latitude": 0,
		"contactType": "",
		"contactNumber": "",
		"title": "",
		"content": "",
		"images": "",
		"address": "",
		"publishTime": "",
		"publishStatus": "",
		"timeStatus": "",
		"auditStatus": "",
		"geoLocation": "",
		"tags": [],
		"contactName": "",
		"contactPhone": "",
		"top": "",
		"completed": 0,
		"categoryId": 0,
		"likeCount": 0,
		"favoriteCount": 0,
		"viewCount": 0,
		"auditRemark": "",
		"auditTime": "",
		"auditUserId": 0,
		"category": {
			"id": 0,
			"name": "",
			"parentId": 0,
			"icon": "",
			"description": "",
			"sort": 0,
			"enabled": 0,
			"enableAudit": 0,
			"tip": "",
			"maxImages": 0,
			"allowTags": "",
			"tags": [
				{
					"id": 0,
					"tagName": "",
					"categoryId": 0,
					"color": "",
					"icon": "",
					"sort": 0,
					"enabled": 0,
					"useCount": 0,
					"isSystem": 0,
					"description": "",
					"sortOrder": ""
				}
			],
			"children": [
				{}
			],
			"postCount": 0,
			"parentName": ""
		},
		"userInfo": {},
		"tagList": [
			{
				"id": 0,
				"tagName": "",
				"categoryId": 0,
				"color": "",
				"icon": "",
				"sort": 0,
				"enabled": 0,
				"useCount": 0,
				"isSystem": 0,
				"description": "",
				"sortOrder": ""
			}
		],
		"avatar": "",
		"isLiked": true,
		"isFavorite": true,
		"nickname": "",
		"feedbackCount": 0,
		"categoryName": "",
		"viewTime": "",
		"viewId": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 获取菜单和轮播图


**接口地址**:`/blade-chat-open/config/menu`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>返回菜单(category=0)和轮播图(category=1)，均按sort_weight升序排序</p>



**请求参数**:


暂无


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RMapStringListUrbMenuVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|UrbMenuVO|UrbMenuVO|
|&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;name|唯一，菜单名称|string||
|&emsp;&emsp;image|图片地址|string||
|&emsp;&emsp;sortWeight|排序权重|string||
|&emsp;&emsp;color|用于存储16进制颜色数据|string||
|&emsp;&emsp;url|用于存储链接地址|string||
|&emsp;&emsp;category|0-菜单，1-滚动图片|integer(int32)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"additionalProperties1": {
			"id": 0,
			"name": "",
			"image": "",
			"sortWeight": "",
			"color": "",
			"url": "",
			"category": 0
		}
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## getCategoryList


**接口地址**:`/blade-chat-open/config/category`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|current|当前页|query|false|string||
|size|每页的数量|query|false|string||
|ascs||query|false|string||
|descs||query|false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RIPageCategoryVO|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data||IPageCategoryVO|IPageCategoryVO|
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;records||array|CategoryVO|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;children||array|Category|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;&emsp;&emsp;parentName||string||
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|&emsp;&emsp;pages||integer(int64)||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {
		"size": 0,
		"records": [
			{
				"id": 0,
				"name": "",
				"parentId": 0,
				"icon": "",
				"description": "",
				"sort": 0,
				"enabled": 0,
				"enableAudit": 0,
				"tip": "",
				"maxImages": 0,
				"allowTags": "",
				"tags": [
					{
						"id": 0,
						"tagName": "",
						"categoryId": 0,
						"color": "",
						"icon": "",
						"sort": 0,
						"enabled": 0,
						"useCount": 0,
						"isSystem": 0,
						"description": "",
						"sortOrder": ""
					}
				],
				"children": [
					{
						"id": 0,
						"name": "",
						"parentId": 0,
						"icon": "",
						"description": "",
						"sort": 0,
						"enabled": 0,
						"enableAudit": 0,
						"tip": "",
						"maxImages": 0,
						"allowTags": "",
						"tags": [
							{
								"id": 0,
								"tagName": "",
								"categoryId": 0,
								"color": "",
								"icon": "",
								"sort": 0,
								"enabled": 0,
								"useCount": 0,
								"isSystem": 0,
								"description": "",
								"sortOrder": ""
							}
						],
						"children": [
							{}
						],
						"postCount": 0,
						"parentName": ""
					}
				],
				"postCount": 0,
				"parentName": ""
			}
		],
		"current": 0,
		"total": 0,
		"pages": 0
	},
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


## 获取分类列表


**接口地址**:`/blade-chat-open/categories`


**请求方式**:`GET`


**请求数据类型**:`application/x-www-form-urlencoded`


**响应数据类型**:`*/*`


**接口描述**:<p>获取所有启用的分类</p>



**请求参数**:


暂无


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|RListCategory|
|400|Bad Request|R|
|401|Unauthorized|R|
|404|Not Found|R|
|405|Method Not Allowed|R|
|415|Unsupported Media Type|R|
|500|Internal Server Error|R|


**响应状态码-200**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|array|Category|
|&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;name|分类名称|string||
|&emsp;&emsp;parentId|上级分类ID|integer(int64)||
|&emsp;&emsp;icon|分类图标|string||
|&emsp;&emsp;description|分类描述|string||
|&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;enableAudit|是否启用审核|integer(int32)||
|&emsp;&emsp;tip|提示信息|string||
|&emsp;&emsp;maxImages|最大图片数|integer(int32)||
|&emsp;&emsp;allowTags|允许的标签|string||
|&emsp;&emsp;tags||array|Tag|
|&emsp;&emsp;&emsp;&emsp;id|主键|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;tagName|标签名称|string||
|&emsp;&emsp;&emsp;&emsp;categoryId|分类ID|integer(int64)||
|&emsp;&emsp;&emsp;&emsp;color|标签颜色|string||
|&emsp;&emsp;&emsp;&emsp;icon|标签图标|string||
|&emsp;&emsp;&emsp;&emsp;sort|排序|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;enabled|是否启用|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;useCount|使用次数|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;isSystem|是否系统标签|integer(int32)||
|&emsp;&emsp;&emsp;&emsp;description||string||
|&emsp;&emsp;&emsp;&emsp;sortOrder||string||
|&emsp;&emsp;children||array|Category|
|&emsp;&emsp;postCount||integer(int32)||
|&emsp;&emsp;parentName||string||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": [
		{
			"id": 0,
			"name": "",
			"parentId": 0,
			"icon": "",
			"description": "",
			"sort": 0,
			"enabled": 0,
			"enableAudit": 0,
			"tip": "",
			"maxImages": 0,
			"allowTags": "",
			"tags": [
				{
					"id": 0,
					"tagName": "",
					"categoryId": 0,
					"color": "",
					"icon": "",
					"sort": 0,
					"enabled": 0,
					"useCount": 0,
					"isSystem": 0,
					"description": "",
					"sortOrder": ""
				}
			],
			"children": [
				{}
			],
			"postCount": 0,
			"parentName": ""
		}
	],
	"msg": ""
}
```


**响应状态码-400**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-401**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-404**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-405**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-415**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```


**响应状态码-500**:


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|状态码|integer(int32)|integer(int32)|
|success|是否成功|boolean||
|data|承载数据|object||
|msg|返回消息|string||


**响应示例**:
```javascript
{
	"code": 0,
	"success": true,
	"data": {},
	"msg": ""
}
```