/**
 * 地区选择服务
 */
const { request } = require('../utils/request');

class RegionService {
  
  /**
   * 获取开放的省份列表
   */
  async getProvinces() {
    try {
      const res = await request({
        url: '/miniapp/region/provinces',
        method: 'GET'
      });
      return res;
    } catch (error) {
      console.error('获取省份列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据省份编码获取城市列表
   * @param {string} provinceCode 省份编码
   */
  async getCities(provinceCode) {
    try {
      const res = await request({
        url: `/miniapp/region/cities?provinceCode=${provinceCode}`,
        method: 'GET'
      });
      return res;
    } catch (error) {
      console.error('获取城市列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据城市编码获取区县列表
   * @param {string} cityCode 城市编码
   */
  async getDistricts(cityCode) {
    try {
      const res = await request({
        url: `/miniapp/region/districts?cityCode=${cityCode}`,
        method: 'GET'
      });
      return res;
    } catch (error) {
      console.error('获取区县列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据区县编码获取乡镇列表
   * @param {string} districtCode 区县编码
   */
  async getTowns(districtCode) {
    try {
      const res = await request({
        url: `/miniapp/region/towns?districtCode=${districtCode}`,
        method: 'GET'
      });
      return res;
    } catch (error) {
      console.error('获取乡镇列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据乡镇编码获取村列表
   * @param {string} townCode 乡镇编码
   */
  async getVillages(townCode) {
    try {
      const res = await request({
        url: `/miniapp/region/villages?townCode=${townCode}`,
        method: 'GET'
      });
      return res;
    } catch (error) {
      console.error('获取村列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据地区编码获取完整地址信息
   * @param {string} regionCode 地区编码
   */
  async getFullAddress(regionCode) {
    try {
      const res = await request({
        url: `/miniapp/region/full-address?regionCode=${regionCode}`,
        method: 'GET'
      });
      return res;
    } catch (error) {
      console.error('获取完整地址信息失败:', error);
      throw error;
    }
  }

  /**
   * 搜索地区
   * @param {string} keyword 搜索关键词
   */
  async searchRegions(keyword) {
    try {
      const res = await request({
        url: `/miniapp/region/search?keyword=${encodeURIComponent(keyword)}`,
        method: 'GET'
      });
      return res;
    } catch (error) {
      console.error('搜索地区失败:', error);
      throw error;
    }
  }

  /**
   * 根据经纬度获取地区信息
   * @param {number} latitude 纬度
   * @param {number} longitude 经度
   */
  async getRegionByLocation(latitude, longitude) {
    try {
      const res = await request({
        url: `/miniapp/region/location?latitude=${latitude}&longitude=${longitude}`,
        method: 'GET'
      });
      return res;
    } catch (error) {
      console.error('根据位置获取地区信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户当前位置对应的地区
   */
  async getCurrentLocationRegion() {
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'wgs84',
        success: async (res) => {
          try {
            const regionRes = await this.getRegionByLocation(res.latitude, res.longitude);
            resolve(regionRes);
          } catch (error) {
            reject(error);
          }
        },
        fail: (error) => {
          console.error('获取位置失败:', error);
          reject(error);
        }
      });
    });
  }

  /**
   * 缓存地区数据到本地存储
   * @param {string} key 缓存键
   * @param {any} data 缓存数据
   */
  cacheRegionData(key, data) {
    try {
      wx.setStorageSync(`region_cache_${key}`, {
        data,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('缓存地区数据失败:', error);
    }
  }

  /**
   * 从本地存储获取缓存的地区数据
   * @param {string} key 缓存键
   * @param {number} expireTime 过期时间（毫秒），默认1小时
   */
  getCachedRegionData(key, expireTime = 3600000) {
    try {
      const cached = wx.getStorageSync(`region_cache_${key}`);
      if (cached && cached.timestamp && (Date.now() - cached.timestamp < expireTime)) {
        return cached.data;
      }
      return null;
    } catch (error) {
      console.error('获取缓存地区数据失败:', error);
      return null;
    }
  }

  /**
   * 清除地区数据缓存
   * @param {string} key 缓存键，不传则清除所有地区缓存
   */
  clearRegionCache(key) {
    try {
      if (key) {
        wx.removeStorageSync(`region_cache_${key}`);
      } else {
        // 清除所有地区缓存
        const info = wx.getStorageInfoSync();
        info.keys.forEach(storageKey => {
          if (storageKey.startsWith('region_cache_')) {
            wx.removeStorageSync(storageKey);
          }
        });
      }
    } catch (error) {
      console.error('清除地区缓存失败:', error);
    }
  }

}

module.exports = new RegionService();
