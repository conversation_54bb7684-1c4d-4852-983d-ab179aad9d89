// pkg_user/pages/institution-detail/institution-detail.js
const { request } = require('../../../utils/request');

Page({
  data: {
    // 机构详情
    institution: null,
    // 加载状态
    loading: true,
    // 机构ID
    institutionId: '',
    // 服务列表
    serviceList: [],
    // 评价列表
    reviewList: [],
    // 当前Tab
    currentTab: 'info' // info, service, review
  },

  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({ institutionId: id });
      this.loadInstitutionDetail(id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载机构详情
  async loadInstitutionDetail(id) {
    try {
      this.setData({ loading: true });

      const result = await request({
        url: `/blade-chat/institution/detail/${id}`,
        method: 'GET'
      });

      if (result.code === 200 && result.data) {
        this.setData({
          institution: result.data,
          loading: false
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: result.data.name || '机构详情'
        });

        // 加载服务列表
        this.loadServiceList(id);
      } else {
        throw new Error(result.msg || '加载失败');
      }
    } catch (error) {
      console.error('加载机构详情失败:', error);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 加载服务列表
  async loadServiceList(id) {
    try {
      const result = await request({
        url: `/blade-chat/institution/services/${id}`,
        method: 'GET'
      });

      if (result.code === 200 && result.data) {
        this.setData({
          serviceList: result.data
        });
      }
    } catch (error) {
      console.error('加载服务列表失败:', error);
    }
  },

  // 切换Tab
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ currentTab: tab });

    if (tab === 'review' && this.data.reviewList.length === 0) {
      this.loadReviewList();
    }
  },

  // 加载评价列表
  async loadReviewList() {
    try {
      const result = await request({
        url: `/blade-chat/institution/reviews/${this.data.institutionId}`,
        method: 'GET'
      });

      if (result.code === 200 && result.data) {
        this.setData({
          reviewList: result.data
        });
      }
    } catch (error) {
      console.error('加载评价列表失败:', error);
    }
  },

  // 拨打电话
  onCallPhone() {
    const phone = this.data.institution?.phone;
    if (phone) {
      wx.makePhoneCall({
        phoneNumber: phone
      });
    }
  },

  // 查看位置
  onViewLocation() {
    const institution = this.data.institution;
    if (institution?.latitude && institution?.longitude) {
      wx.openLocation({
        latitude: parseFloat(institution.latitude),
        longitude: parseFloat(institution.longitude),
        name: institution.name,
        address: institution.address
      });
    }
  },

  // 分享机构
  onShareAppMessage() {
    const institution = this.data.institution;
    return {
      title: `推荐机构：${institution?.name || '本地机构'}`,
      path: `/pkg_user/pages/institution-detail/institution-detail?id=${this.data.institutionId}`,
      imageUrl: institution?.logo || ''
    };
  },

  // 收藏机构
  async onToggleFavorite() {
    try {
      const result = await request({
        url: '/blade-chat/institution/favorite',
        method: 'POST',
        data: {
          institutionId: this.data.institutionId,
          action: this.data.institution.isFavorite ? 'remove' : 'add'
        }
      });

      if (result.code === 200) {
        this.setData({
          'institution.isFavorite': !this.data.institution.isFavorite
        });

        wx.showToast({
          title: this.data.institution.isFavorite ? '已收藏' : '已取消收藏',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('收藏操作失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 预约服务
  onBookService(e) {
    const service = e.currentTarget.dataset.service;
    wx.showModal({
      title: '预约服务',
      content: `确定要预约"${service.name}"服务吗？`,
      success: (res) => {
        if (res.confirm) {
          this.bookService(service);
        }
      }
    });
  },

  // 执行预约
  async bookService(service) {
    try {
      const result = await request({
        url: '/blade-chat/institution/book',
        method: 'POST',
        data: {
          institutionId: this.data.institutionId,
          serviceId: service.id
        }
      });

      if (result.code === 200) {
        wx.showToast({
          title: '预约成功',
          icon: 'success'
        });
      } else {
        throw new Error(result.msg || '预约失败');
      }
    } catch (error) {
      console.error('预约失败:', error);
      wx.showToast({
        title: error.message || '预约失败',
        icon: 'none'
      });
    }
  }
});
