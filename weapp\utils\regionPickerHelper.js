/**
 * 地区选择器辅助工具
 * 提供统一的底部导航栏控制和其他通用功能
 */

/**
 * 处理地区选择器显示状态变化
 * 自动控制底部导航栏的显示/隐藏
 * @param {Object} e 事件对象
 * @param {Object} options 配置选项
 */
function handleRegionPickerShowChange(e, options = {}) {
  const { show } = e.detail;
  const { 
    hideTabBar = true,           // 是否隐藏底部导航栏
    animation = true,            // 是否使用动画
    onShow = null,              // 显示时的回调
    onHide = null,              // 隐藏时的回调
    customTabBarSelector = null  // 自定义底部导航栏选择器
  } = options;

  console.log('地区选择器显示状态变化:', show);

  if (show) {
    // 地区选择器显示时
    if (hideTabBar) {
      // 如果有自定义底部导航栏
      if (customTabBarSelector) {
        try {
          const customTabBar = getCurrentPages()[getCurrentPages().length - 1].selectComponent(customTabBarSelector);
          if (customTabBar) {
            customTabBar.setData({ show: false });
          }
        } catch (error) {
          console.warn('隐藏自定义底部导航栏失败:', error);
        }
      } else {
        // 隐藏系统底部导航栏
        wx.hideTabBar({
          animation: animation,
          fail: (err) => {
            console.warn('隐藏底部导航栏失败:', err);
          }
        });
      }
    }
    
    // 执行显示回调
    if (typeof onShow === 'function') {
      onShow();
    }
  } else {
    // 地区选择器隐藏时
    if (hideTabBar) {
      // 如果有自定义底部导航栏
      if (customTabBarSelector) {
        try {
          const customTabBar = getCurrentPages()[getCurrentPages().length - 1].selectComponent(customTabBarSelector);
          if (customTabBar) {
            customTabBar.setData({ show: true });
          }
        } catch (error) {
          console.warn('显示自定义底部导航栏失败:', error);
        }
      } else {
        // 显示系统底部导航栏
        wx.showTabBar({
          animation: animation,
          fail: (err) => {
            console.warn('显示底部导航栏失败:', err);
          }
        });
      }
    }
    
    // 执行隐藏回调
    if (typeof onHide === 'function') {
      onHide();
    }
  }
}

/**
 * 为页面添加地区选择器相关的方法
 * 可以通过 mixin 的方式使用
 */
const regionPickerMixin = {
  /**
   * 地区选择器显示状态变化处理
   */
  onRegionShowChange(e) {
    handleRegionPickerShowChange(e, {
      hideTabBar: true,
      animation: true,
      customTabBarSelector: this.data.customTabBarSelector || null
    });
  },

  /**
   * 地区选择器确认事件处理
   */
  onRegionConfirm(e) {
    const { selectedRegions, lastRegion, fullAddress } = e.detail;
    
    console.log('选择的地区:', {
      selectedRegions,
      lastRegion,
      fullAddress
    });

    // 更新页面数据
    this.setData({
      selectedLocation: fullAddress,
      selectedLocationName: lastRegion ? lastRegion.regionName : fullAddress,
      selectedRegion: lastRegion,
      selectedRegions: selectedRegions,
      showRegionPicker: false
    });

    // 显示选择成功提示
    if (lastRegion) {
      wx.showToast({
        title: `已选择: ${lastRegion.regionName}`,
        icon: 'success',
        duration: 1500
      });
    }

    // 触发自定义事件，让页面可以进行额外处理
    if (typeof this.onRegionSelected === 'function') {
      this.onRegionSelected({
        selectedRegions,
        lastRegion,
        fullAddress
      });
    }
  },

  /**
   * 关闭地区选择器
   */
  onRegionClose() {
    this.setData({ showRegionPicker: false });
  },

  /**
   * 显示地区选择器
   */
  showRegionPicker() {
    this.setData({ showRegionPicker: true });
  }
};

/**
 * 检查当前页面是否有底部导航栏
 * @returns {boolean} 是否有底部导航栏
 */
function hasTabBar() {
  try {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const route = currentPage.route;
    
    // 检查 app.json 中的 tabBar 配置
    const app = getApp();
    if (app.globalData && app.globalData.tabBar && app.globalData.tabBar.list) {
      return app.globalData.tabBar.list.some(item => item.pagePath === route);
    }
    
    return false;
  } catch (error) {
    console.warn('检查底部导航栏状态失败:', error);
    return false;
  }
}

/**
 * 获取安全区域信息
 * @returns {Object} 安全区域信息
 */
function getSafeAreaInfo() {
  try {
    const systemInfo = wx.getSystemInfoSync();
    return {
      safeAreaInsetBottom: systemInfo.safeArea ? systemInfo.windowHeight - systemInfo.safeArea.bottom : 0,
      screenHeight: systemInfo.screenHeight,
      windowHeight: systemInfo.windowHeight,
      statusBarHeight: systemInfo.statusBarHeight
    };
  } catch (error) {
    console.warn('获取安全区域信息失败:', error);
    return {
      safeAreaInsetBottom: 0,
      screenHeight: 0,
      windowHeight: 0,
      statusBarHeight: 0
    };
  }
}

module.exports = {
  handleRegionPickerShowChange,
  regionPickerMixin,
  hasTabBar,
  getSafeAreaInfo
};
