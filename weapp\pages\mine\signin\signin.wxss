.signin-container {
  min-height: 100vh;
  background-color: #fafafa;
  padding: 32rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 48rpx;
  color: #333;
}

.page-title {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  color: #333;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #999;
}

/* 积分信息 */
.points-section {
  margin-bottom: 32rpx;
}

.points-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.04);
}

.points-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.points-label {
  font-size: 28rpx;
  color: #999;
}

.points-value {
  font-size: 48rpx;
  font-weight: 600;
  color: #ff8383;
}

.points-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 64rpx;
  background: #fff;
  border: 2rpx solid #ff8383;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  background: #fff5f5;
  border-color: #ff6a6a;
}

.action-text {
  font-size: 26rpx;
  color: #ff8383;
}

/* 签到区域 */
.signin-section {
  margin-bottom: 32rpx;
}

.signin-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.04);
}

.signin-status {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  background: #fff5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  transition: all 0.3s ease;
}

.status-icon.signed {
  background: #ff8383;
}

.icon-text {
  font-size: 36rpx;
  color: #ff8383;
}

.status-info {
  flex: 1;
}

.status-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.status-desc {
  display: block;
  font-size: 26rpx;
  color: #999;
}

.signin-btn-wrap {
  margin-bottom: 24rpx;
}

.signin-btn {
  width: 100%;
  height: 96rpx;
  background: #ff8383;
  border: none;
  border-radius: 48rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(255, 131, 131, 0.10);
  transition: all 0.3s ease;
}

.signin-btn:active {
  background: #ff6a6a;
}

.signin-btn.signed {
  background: #fff5f5;
  color: #ff8383;
  border: 2rpx solid #ff8383;
}

.signin-btn.signing {
  background: #ffbcbc;
  color: #fff;
}

.reward-info {
  text-align: center;
}

.reward-text {
  font-size: 28rpx;
  color: #ff8383;
  font-weight: 600;
}

/* 连续签到 */
.continuous-section {
  margin-bottom: 32rpx;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  color: #333;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
}

.title-desc {
  font-size: 26rpx;
  color: #999;
}

.continuous-rewards {
  background: #fff;
  border-radius: 24rpx;
  padding: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.04);
}

.reward-item {
  display: flex;
  align-items: center;
  padding: 20rpx 16rpx;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  background: #fff;
  border: 2rpx solid #f5f5f5;
  transition: all 0.3s ease;
  position: relative;
}

.reward-item:last-child {
  margin-bottom: 0;
}

.reward-item.achieved {
  background: linear-gradient(135deg, #fff5f5 0%, #ffeaea 100%);
  border-color: #ffcccb;
  box-shadow: 0 4rpx 12rpx rgba(255, 131, 131, 0.1);
}

/* 进度指示器 */
.reward-indicator {
  flex: 0 0 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.indicator-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f5f5f5;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.indicator-icon.completed {
  background: #ff8383;
  border-color: #ff8383;
  color: #fff;
}

.indicator-icon .icon-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #fff;
}

.indicator-icon.completed .icon-text {
  color: #fff;
  font-size: 24rpx;
}

.reward-days {
  flex: 0 0 60rpx;
  display: flex;
  align-items: center;
}

.days-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
}

.reward-item.achieved .days-text {
  color: #ff8383;
}

.reward-points {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
}

.points-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  background: #f8f8f8;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid #eee;
}

.reward-item.achieved .points-text {
  background: #ff8383;
  color: #fff;
  border-color: #ff8383;
}

.reward-status {
  flex: 0 0 100rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.status-text {
  font-size: 24rpx;
  color: #999;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  background: #f5f5f5;
  border: 1rpx solid #eee;
}

.reward-item.achieved .status-text {
  color: #fff;
  background: #ff8383;
  border-color: #ff8383;
}

/* 签到记录 */
.record-section {
  margin-bottom: 32rpx;
}

.calendar-grid {
  background: #fff;
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.04);
}

.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 16rpx;
}

.header-item {
  text-align: center;
  font-size: 26rpx;
  color: #999;
  padding: 12rpx 0;
}

.calendar-body {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
}

.calendar-day {
  position: relative;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.calendar-day.today {
  background: #ff8383;
  color: #fff;
}

.calendar-day.signed {
  background: #fff5f5;
  color: #ff8383;
}

.calendar-day.other-month {
  opacity: 0.3;
}

.day-text {
  font-size: 26rpx;
  font-weight: 500;
}

.signed-dot {
  position: absolute;
  bottom: 4rpx;
  width: 8rpx;
  height: 8rpx;
  background: #ff8383;
  border-radius: 50%;
}

/* 签到规则 */
.rules-section {
  margin-bottom: 32rpx;
}

.rules-list {
  background: #fff;
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.04);
}

.rule-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.rule-item:last-child {
  margin-bottom: 0;
}

.rule-dot {
  font-size: 24rpx;
  color: #ff8383;
  margin-right: 12rpx;
  margin-top: 4rpx;
}

.rule-text {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 分享区域 */
.share-section {
  margin-bottom: 32rpx;
}

.share-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.share-info {
  width: 100%;
}

.share-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.share-desc {
  display: block;
  font-size: 26rpx;
  color: #999;
}

.share-btn {
  margin-left: 120rpx;
  width: 160rpx;
  height: 64rpx;
  background: #ff8383;
  border: none;
  border-radius: 32rpx;
  color: #fff;
  font-size: 26rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.share-btn:active {
  background: #ff6a6a;
} 